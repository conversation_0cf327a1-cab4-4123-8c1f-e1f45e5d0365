repos:
  - repo: local
    hooks:
      - id: isort
        name: isort
        entry: isort
        language: python
        types: [python]
        exclude: '.*/migrations/.*'
        args: [
            '--line-length=88',
            '--multi-line=3',
            '--force-grid-wrap=0',
            '--trailing-comma',
            '--use-parentheses',
            '--ensure-newline-before-comments',
            '.'
        ]
      - id: black
        name: black
        entry: black
        language: python
        language_version: python3.10
        types: [python]
      - id: flake8
        name: flake8
        entry: flake8
        language: python
        types: [python]
        exclude: migrations
      - id: bandit
        name: bandit
        entry: bandit
        args: ["--silent", "--severity-level=medium", "--format=txt"]
        language: python
        types: [python]
        exclude: migrations
  - repo: local
    hooks:
      - id: pytest-check
        name: pytest-check
        entry: coverage run -m pytest
        language: system
        pass_filenames: false
        language: python
        types: [python]
  - repo: https://github.com/jorisroovers/gitlint
    rev: v0.19.1
    hooks:
      - id: gitlint
        stages: [commit-msg]
