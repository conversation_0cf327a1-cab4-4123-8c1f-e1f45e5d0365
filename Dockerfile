FROM dockerhub.agribusiness-brain.br.experian.eeca/python:3.12-alpine

ARG DOCKER_TAG=

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV GDAL_LIBRARY_PATH=/usr/lib/libgdal.so
ENV GEOS_LIBRARY_PATH=/usr/lib/libgeos_c.so

RUN apk update
RUN apk upgrade

RUN apk add  --virtual .build-deps \
        gcc \
        python3-dev \
        build-base \
        curl-dev \
        musl-dev \
        gdal \
        postgresql-dev \
        proj-util \
        proj-dev

RUN apk add \
        nginx \
        supervisor \
        postgresql-libs \
        linux-headers \
        gdal-dev \
        geos-dev

COPY supervisor/supervisord.conf /etc/supervisord.conf
COPY supervisor/supervisor.d /etc/supervisor.d

COPY pb_agro_report_rgca_api /usr/pb_agro_report_rgca_api
COPY images /usr/images
COPY alembic /usr/alembic
COPY alembic.ini /usr/alembic.ini
COPY Pipfile Pipfile.lock /usr/

WORKDIR /usr

COPY cert.pem /usr/local/share/ca-certificates/cert.pem

RUN update-ca-certificates && \
        sed -i 's/^version = \"\(.*\)\"/version = "'${DOCKER_TAG}'\"/g' pyproject.toml & \
        pip3 install -i https://nexus.agribusiness-brain.br.experian.eeca/repository/pypi-hub/simple/ pip --upgrade && \
        pip3 install pipenv pytest && \
        pip3 install --no-cache-dir -i https://nexus.agribusiness-brain.br.experian.eeca/repository/pypi-hub/simple/ \
        GDAL==3.9.2 --config-settings=global-option=build_ext --config-settings=global-option=$(gdal-config --cflags) && \
        pipenv install --deploy --system

# RUN poetry run alembic upgrade head

RUN apk --purge del .build-deps && \
        rm -rf /var/cache/apk/*

CMD ["supervisord", "-n", "-c", "/etc/supervisord.conf"]
