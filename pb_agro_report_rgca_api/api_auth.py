import os

from fastapi import Request
from iam_token_validation import IamTokenValidationService
from requests.exceptions import HTTPError


def get_iam_token(request: Request) -> str:
    try:
        authorization: str = request.headers.get("Authorization")
        if not authorization or not authorization.startswith("Bearer "):
            raise HTTPError(401, "Unauthorized")
        token = authorization.split(" ")[1]

        api_users = os.getenv("API_USERS_BASE_URL")
        iam_service = IamTokenValidationService(api_users)
        user_data = iam_service.handle(token, "AGRO_REPORT")

        if user_data:
            return user_data
    except HTTPError as err:
        if err.response and err.response.status_code == 404:
            raise HTTPError(403, "Permission Denied")
        raise HTTPError(401, "Unauthorized")
    except Exception:
        raise HTTPError(401, "Unauthorized")
