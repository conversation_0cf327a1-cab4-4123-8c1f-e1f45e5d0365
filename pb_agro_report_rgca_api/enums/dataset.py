from enum import Enum


class Dataset(str, Enum):
    ARMAZENS = "Presença de Armazéns"
    ANA_PIVO = "Presença de Pivôs de Irrigação"
    EMBARGOS_IBAMA = "Embargos IBAMA (Vetor)"
    EMBARGOS_LDI = "Embargos LDI-PARÁ (Vetor)"
    EMBARGOS_ICMBIO = "Embargos ICMBIO (Vetor)"
    EMBARGOS_SEMA_MT = "Embargos SEMA-MT (Vetor)"
    PRODES = "PRODES"
    QUILOMBOS = "Quilombos"
    TERRAS_INDIGENAS = "Terras Indígenas"
    UNIDADES_CONSERVACAO = "Unidades de Conservação"
    PRA = "PRA"
    EMBARGOS_IBAMA_LISTA = "Embargos IBAMA (Lista)"
    EMBARGOS_LDI_LISTA = "Embargos LDI-PARÁ (Lista)"
    EMBARGOS_ICMBIO_LISTA = "Embargos ICMBIO (Lista)"
    EMBARGOS_SEMA_MT_LISTA = "Embargos SEMA-MT (Lista)"
    TRABALHO_ESCRAVO = "Trabalho Escravo (Lista)"
