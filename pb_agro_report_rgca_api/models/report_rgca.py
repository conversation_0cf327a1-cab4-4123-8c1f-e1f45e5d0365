from sqlalchemy import Column, DateTime, Integer, String
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

from pb_agro_report_rgca_api.enums.report_status import ReportStatus

Base = declarative_base()


class ReportRGCA(Base):
    __tablename__ = "report_rgca"
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    requester = Column(String, nullable=False)
    webhook = Column(String, nullable=True)
    status = Column(String, server_default=ReportStatus.PENDING.value)
    client_document = Column(String, nullable=False)
    filename = Column(String, nullable=True)
    external_id = Column(Integer, nullable=False)
    succeeded_scores = Column(Integer, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
