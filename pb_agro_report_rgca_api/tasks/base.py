import os

from celery import Task
from celery.utils.log import get_task_logger
from requests import HTTPError
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from pb_agro_report_rgca_api.enums.report_status import ReportStatus
from pb_agro_report_rgca_api.models.report_rgca import ReportRGCA
from pb_agro_report_rgca_api.services.agro_report_api import AgroReportApiService
from pb_agro_report_rgca_api.services.teams_messages import TeamsMessagesService
from pb_agro_report_rgca_api.utils.build_failure import build_failure_msg

logger = get_task_logger(__name__)


class TaskBase(Task):
    def __init__(self) -> None:
        self.sessions = {}
        DB_USER_APP = os.getenv("DB_USER_APP")
        DB_PASSWORD_APP = os.getenv("DB_PASSWORD_APP")
        DB_HOST_APP = os.getenv("DB_HOST_APP")
        DB_PORT_APP = os.getenv("DB_PORT_APP", "5432")
        DB_DATABASE_APP = os.getenv("DB_DATABASE_APP")
        DB_URL = f"postgresql://{DB_USER_APP}:{DB_PASSWORD_APP}@{DB_HOST_APP}:{DB_PORT_APP}/{DB_DATABASE_APP}"  # noqa
        self.engine = create_engine(DB_URL)
        super().__init__()

    def before_start(self, task_id, args, kwargs):
        session = sessionmaker(self.engine)
        self.sessions[task_id] = session()
        self.set_new_status(
            task_id=task_id,
            output_local_file="",
            new_status=ReportStatus.PROCESSING.value,
            **kwargs,
        )
        super().before_start(task_id, args, kwargs)

    def on_success(self, retval, task_id, args, kwargs):
        output_local_file = retval.get("output_local_file")

        self.set_new_status(
            task_id=task_id,
            output_local_file=output_local_file,
            new_status=ReportStatus.DONE.value,
            **kwargs,
        )

    def on_retry(self, exc, task_id, args, kwargs, einfo):
        pass

    def on_failure(self, exc: Exception, task_id, args, kwargs, einfo):
        self.set_new_status(
            task_id=task_id,
            new_status=ReportStatus.ERROR.value,
            output_local_file="",
            errors=True,
            **kwargs,
        )

        subject, message = build_failure_msg(exc)
        TeamsMessagesService.send_teams_message(subject=subject, message=message)

    def after_return(self, status, retval, task_id, args, kwargs, einfo):
        session = self.sessions.pop(task_id)
        session.close()

    def set_new_status(
        self,
        task_id,
        user_id,
        report_id,
        rgca_id,
        output_local_file,
        new_status,
        **kwargs,
    ):
        agroreport_api_service = AgroReportApiService(user_id=user_id)
        session = self.sessions[task_id]
        report_rgca = session.query(ReportRGCA).filter(ReportRGCA.id == rgca_id).first()

        try:
            report_rgca.status = new_status
            params = {
                "status": new_status,
                "amount_lines": report_rgca.succeeded_scores or 0,
            }
            if output_local_file:
                report_rgca.filename = output_local_file
                agroreport_api_service.post(
                    path=f"reports/{report_id}/exports/upload",
                    params={"file_format": "XLSX"},
                    files={"file": open(output_local_file, "rb")},
                )

            if kwargs.get("errors"):
                params["errors"] = {
                    "error_in_document": "Erro ao tentar ler o arquivo"  # noqa
                }

            agroreport_api_service.patch(path=f"reports/{report_id}", json=params)

            session.commit()
            session.refresh(report_rgca)

        except Exception as e:
            session.rollback()
            raise HTTPError(
                f"An error ocurred when trying to update the "
                f"status to {new_status}. "
                f"Error: {e}"
            )

    @property
    def session(self):
        return self.sessions[self.request.id]
