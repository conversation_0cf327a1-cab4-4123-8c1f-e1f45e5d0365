import os

from pb_agro_report_rgca_api import celery_app
from pb_agro_report_rgca_api.services.diagnosis_service import DiagnosisService
from pb_agro_report_rgca_api.tasks.base import TaskBase


@celery_app.task(bind=True, name="diagnosis_report_task", base=TaskBase)
def diagnosis_report_task(self, document_filename, sub_modules, **kwargs):
    client_document = kwargs.get("client_document", "test")
    path_file = os.path.join(os.getenv("MEDIA_DIR"), client_document, document_filename)
    diagnosis_service = DiagnosisService()
    response = diagnosis_service.generate_report(
        path_file, sub_modules, client_document
    )
    return response
