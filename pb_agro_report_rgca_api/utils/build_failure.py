import re
import traceback


def extract_relevant_path(file_path, root_folder="pb_agro_report_rgca_api"):
    start_index = file_path.find(root_folder)
    if start_index != -1:
        return file_path[start_index:]
    return file_path


def extract_traceback_info(exception: Exception, traceback_content: list):
    file_pattern = re.compile(r'File "([^"]+)", line (\d+), in (\w+)')
    line = traceback_content[-1]
    last_match = file_pattern.search(line)
    if last_match:
        file_path = last_match.group(1)
        line_number = last_match.group(2)
        function_name = last_match.group(3)
        error_message = str(exception)
        return file_path, line_number, function_name, error_message
    return "NOT_FOUND", "NOT_FOUND", "NOT_FOUND", "NOT_FOUND"


def build_failure_msg(exception):
    traceback_content = traceback.format_tb(exception.__traceback__)
    (
        error_file,
        line_number,
        function_name,
        error_message,
    ) = extract_traceback_info(exception, traceback_content)

    path_error = extract_relevant_path(error_file)

    if error_file == "NOT_FOUND":
        teams_subject_message = "An unknown error occurred. Please check the traceback."
        teams_error_message = f"**Traceback**: {traceback_content}\n\n"
    else:
        teams_subject_message = f"Error found in {path_error}"
        teams_error_message = (
            f"**Path**: {path_error}\n\n"
            f"**Line**: {line_number}\n\n"
            f"**Function**: {function_name}\n\n"
            f"**Error**: {error_message}\n\n\n"
            f"**Traceback**: {traceback.format_exc()}\n\n"
        )

    return teams_subject_message, teams_error_message
