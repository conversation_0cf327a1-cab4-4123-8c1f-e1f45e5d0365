import os

import pymongo

from pb_agro_report_rgca_api.enums.dataset import Dataset
from pb_agro_report_rgca_api.services.intersection_service import IntersectorService


class SoilUseService:
    def __init__(self):
        self.client = pymongo.MongoClient(
            host=os.getenv("MONGO_DB_HOST"),
            username=os.getenv("MONGO_DB_USER"),
            password=os.getenv("MONGO_DB_PASSWORD"),
            port=int(os.getenv("MONGO_DB_PORT", 0)),
        )
        self.collection = self.client[
            os.getenv("MONGO_DB_DATABASE", "TEST")
        ].soil_use_v3
        self.culture_year_map = {
            "corn": {"translate": "MILHO", "years": [2022, 2023, 2024]},
            "corn2": {"translate": "MILHO 2ª SAFRA", "years": [2023]},
            "soybeans": {"translate": "SOJA", "years": [2022, 2023, 2024]},
            "sugar_cane": {"translate": "CANA", "years": [2022, 2023, 2024]},
            "cotton": {"translate": "ALGODÃO", "years": [2022, 2023, 2024]},
            "coffee": {"translate": "CAFÉ", "years": [2021, 2022, 2023]},
            "pasture": {"translate": "PASTO", "years": [2021, 2022, 2023]},
            "forest_formation": {
                "translate": "VEGETAÇÃO NATIVA",
                "years": [2021, 2022, 2023],
            },
        }
        self.default_columns = {
            "Soja (2021/22)": "-",
            "Soja (2022/23)": "-",
            "Soja (2023/24)": "-",
            "Milho (2021/22)": "-",
            "Milho (2022/23)": "-",
            "Milho (2023/24)": "-",
            "Milho 2ª safra (2022/23)": "-",
            "Algodão (2021/22)": "-",
            "Algodão (2022/23)": "-",
            "Algodão (2023/24)": "-",
            "Cana (2021/22)": "-",
            "Cana (2022/23)": "-",
            "Cana (2023/24)": "-",
            "Café (2020/21)": "-",
            "Café (2021/22)": "-",
            "Café (2022/23)": "-",
            "Pasto (2020/21)": "-",
            "Pasto (2021/22)": "-",
            "Pasto (2022/23)": "-",
            "Vegetação nativa (2020/21)": "-",
            "Vegetação nativa (2021/22)": "-",
            "Vegetação nativa (2022/23)": "-",
            "Presença de Pivôs de Irrigação": "-",
            "Presença de Armazéns": "-",
        }

    def organize_results(self, results, cars):
        organized_results = {car: self.default_columns.copy() for car in cars}
        if results:
            for result in results:
                car = result.get("car")
                year = int(result.get("year"))
                organized_results.setdefault(car, self.default_columns.copy())

                for key in self.culture_year_map.keys():
                    if key in result:
                        translate = self.culture_year_map[key]["translate"].capitalize()
                        if year in self.culture_year_map[key]["years"]:
                            column_name = f"{translate} ({year - 1}/{str(year)[-2:]})"
                            organized_results[car][column_name] = (
                                "SIM" if result[key] > 0 else "NÃO"
                            )

        return organized_results

    def get_soil_use(self, cars):
        query = {"car": {"$in": cars}}

        results = list(self.collection.find(query, {"_id": 0}))
        results = self.organize_results(results=results, cars=cars)

        return results

    def check_landmarks(self, input_df):
        intersector_service = IntersectorService()

        df_filtered = input_df.drop_duplicates(subset=["car"], keep="first")

        for dataset in [Dataset.ANA_PIVO, Dataset.ARMAZENS]:
            for index, row in df_filtered.iterrows():
                car = row["car"]
                if "geom" in row and row["geom"] is not None:
                    try:
                        has_intersection = intersector_service.get_intersection(
                            row, dataset
                        )
                        input_df.loc[input_df["car"] == car, dataset.value] = (
                            has_intersection
                        )
                    except Exception:
                        input_df.loc[input_df["car"] == car, dataset.value] = "-"
        return input_df
