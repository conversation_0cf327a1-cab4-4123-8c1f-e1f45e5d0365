import numpy as np
import pandas as pd
from agri_data_crypto.main import Main
from sqlalchemy import MetaData, Table, desc, select

from pb_agro_report_rgca_api.services.rectification_car import RectificationCarService
from pb_agro_report_rgca_api.services.utils_service import UtilsService


class LandService:
    def __init__(self):
        self.utils = UtilsService()
        self.engine = self.utils.get_engine()
        self.metadata = MetaData()
        self.cripter = Main()
        self.rectified_service = RectificationCarService()

    def process_rectified_car(self, input_df):
        input_df = self.rectified_service.process_retified_car(input_df)
        return input_df

    def get_car_metadata(self, input_df):
        table = "mgi_car_property_limits_a"
        columns = ["federal_car_code", "ibge_code", "state", "area", "status"]
        car_list = input_df["car"].unique().tolist()
        if "-" in car_list:
            car_list.remove("-")
        rows = self._execute_stmt(table, columns, car_list)
        car_metadata_df = pd.DataFrame(rows, columns=columns)
        input_df = input_df.merge(
            car_metadata_df, left_on="car", right_on="federal_car_code", how="left"
        )
        input_df["Região"] = input_df["state"].map(
            self.utils.get_region_by_state_abbrev
        )
        input_df["status"] = input_df["status"].map(
            self.utils.get_car_situation_description
        )
        input_df["area"] = input_df["area"].apply(
            lambda x: round(x, 2) if isinstance(x, (int, float)) else x
        )
        columns_to_replace = input_df.columns.difference(["geom"])
        input_df[columns_to_replace] = input_df[columns_to_replace].replace(
            [None, np.nan, "", " "], "-"
        )
        input_df = self.utils.remove_columns(input_df, ["federal_car_code"])
        input_df = self.utils.rename_columns(
            input_df,
            {
                "ibge_code": "Código Município",
                "state": "Estado",
                "area": "Área total do CAR (ha)",
                "status": "Status CAR",
            },
        )
        return input_df

    def _anonymize_name(self, df):
        for col in ["nome1", "nome2", "nome3"]:
            df[col] = df[col].apply(lambda x: self.utils.anonymize_name(x))
        return df

    def get_owners_dataframe(self, input_df):
        results = self._get_data_from_database(input_df)
        combined_data = self._combine_data(results)
        combined_df = self._mount_dataframe(combined_data)
        decrypted_df = self._decrypt_documents(combined_df)
        input_df = input_df.merge(decrypted_df, on="car", how="left")
        input_df = self._anonymize_name(input_df)
        input_df = self.utils.rename_columns(
            input_df,
            {
                "nome1": "Proprietário1",
                "nome2": "Proprietário2",
                "nome3": "Proprietário3",
                "doc1": "Documento1",
                "doc2": "Documento2",
                "doc3": "Documento3",
            },
        )
        return input_df

    def _execute_stmt(self, table_name, columns, car_list):
        table = Table(table_name, self.metadata, autoload_with=self.engine)
        stmt = (
            select(*[table.c[col] for col in columns])
            .distinct(table.c["federal_car_code"])
            .where(table.c["federal_car_code"].in_(car_list))
            .order_by(
                table.c["federal_car_code"],  # deve vir primeiro
                desc(table.c["id"]),
            )
        )
        with self.engine.connect() as conn:
            result = conn.execute(stmt).fetchall()
        return result

    def _get_data_from_database(self, input_df):
        data = [
            {"table": "mgi_car_property_relations_m", "state": ""},
            {"table": "sema_imac_ac_car_a", "state": "AC"},
            {"table": "sema_mt_car_a", "state": "MT"},
            {"table": "semarh_to_car_a", "state": "TO"},
            {"table": "semas_pa_car_a", "state": "PA"},
        ]
        columns = ["federal_car_code", "documents", "names_receipt"]
        results = []
        for item in data:
            state = item["state"]
            update_df = input_df[input_df["car"].str.startswith(state)]
            car_list = update_df["car"].unique().tolist()
            if car_list:
                results.append(self._execute_stmt(item["table"], columns, car_list))

        return results

    def _combine_data(self, data):
        combined_data = {}
        for sublist in data:
            for car, docs, names in sublist:
                if car not in combined_data:
                    combined_data[car] = {"docs": [], "names": []}

                for i, doc in enumerate(docs):
                    if doc not in combined_data[car]["docs"]:
                        combined_data[car]["docs"].append(doc)
                        combined_data[car]["names"].append(names[i])

        return combined_data

    def _mount_dataframe(self, combined_data):
        df_data = []
        for car, info in combined_data.items():
            docs = info["docs"][:3] + ["-"] * (3 - len(info["docs"][:3]))
            names = info["names"][:3] + ["-"] * (3 - len(info["names"][:3]))
            df_data.append([car] + names + docs)

        df = pd.DataFrame(
            df_data,
            columns=["car", "nome1", "nome2", "nome3", "doc1", "doc2", "doc3"],
        )
        return df

    def _decrypt_documents(self, df):
        documents = list(
            set(
                df["doc1"].unique().tolist()
                + df["doc2"].unique().tolist()
                + df["doc3"].unique().tolist()
            )
        )
        if "-" in documents:
            documents.remove("-")

        decrypted_documents = {"-": "-"}
        for doc in documents:
            document = self.cripter.decrypt_aes_ctr({"data": doc})
            decrypted_documents[doc] = self.utils.remask_document(document)

        df["doc1"] = df["doc1"].replace(decrypted_documents)
        df["doc2"] = df["doc2"].replace(decrypted_documents)
        df["doc3"] = df["doc3"].replace(decrypted_documents)

        return df
