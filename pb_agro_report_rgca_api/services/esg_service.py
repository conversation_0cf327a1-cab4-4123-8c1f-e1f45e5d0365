from pb_agro_report_rgca_api.enums.dataset import Dataset
from pb_agro_report_rgca_api.services.document_service import DocumentService
from pb_agro_report_rgca_api.services.intersection_service import IntersectorService
from pb_agro_report_rgca_api.services.utils_service import UtilsService


class ESGService:
    def __init__(self):
        self.intersector = IntersectorService()
        self.document_service = DocumentService()
        self.utils_service = UtilsService()

    def process_esg_intersections(self, input_df):
        input_df = self.intersector.get_pra_intersections(input_df)
        input_df = self._process_intersection(input_df)
        input_df = self._process_documents(input_df)
        return input_df

    def _process_documents(self, input_df):
        datasets = [
            Dataset.EMBARGOS_IBAMA_LISTA,
            Dataset.EMBARGOS_SEMA_MT_LISTA,
            Dataset.EMBARGOS_LDI_LISTA,
            Dataset.EMBARGOS_ICMBIO_LISTA,
            Dataset.TRABALHO_ESCRAVO,
        ]
        input_df, document_list = self.document_service.get_encrypted_docs(input_df)
        for dataset in datasets:
            input_df = self.document_service.is_document_in_list(
                input_df=input_df,
                dataset=dataset,
                documents=document_list,
            )
        input_df = self.utils_service.remove_columns(input_df, ["encrypted"])
        return input_df

    def _process_intersection(self, input_df):
        datasets = [
            Dataset.EMBARGOS_IBAMA,
            Dataset.EMBARGOS_LDI,
            Dataset.EMBARGOS_SEMA_MT,
            Dataset.EMBARGOS_ICMBIO,
            Dataset.PRODES,
            Dataset.QUILOMBOS,
            Dataset.TERRAS_INDIGENAS,
            Dataset.UNIDADES_CONSERVACAO,
        ]
        filtered_df = input_df.drop_duplicates(subset=["car"], keep="first")
        for dataset in datasets:
            for _, row in filtered_df.iterrows():
                car = row["car"]
                if "geom" in row and row["geom"] is not None:
                    has_intersection = self.intersector.get_intersection(
                        row=row,
                        dataset=dataset,
                    )
                    input_df.loc[input_df["car"] == car, dataset.value] = (
                        has_intersection
                    )
                else:
                    input_df.loc[input_df["car"] == car, dataset.value] = "-"
        return input_df
