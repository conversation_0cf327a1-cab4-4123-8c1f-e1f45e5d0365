import os
import traceback

import pymsteams


class TeamsMessagesService:

    @staticmethod
    def send_teams_message(subject, message):
        teams_message = pymsteams.connectorcard(
            os.getenv("TEAMS_WEBHOOK_RGCA_API", None)
        )
        if teams_message:
            try:
                teams_message.title(subject)
                teams_message.text(message[0:7800])
                teams_message.send()
                tam = round(len(message) / 7800)
                for i in range(1, tam):
                    teams_message.text(message[(7800 * i) + 1 : 7800 * (i + 1)])  # noqa
                    teams_message.send()
            except Exception:
                traceback.print_exc()
