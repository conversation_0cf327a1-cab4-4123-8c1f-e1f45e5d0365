import os
import tempfile

import boto3
import requests


class FileService:
    def __init__(self):
        self.output_bucket = os.getenv("RGCA_BUCKET", "")

    def remove_local_file(self, output_local_file):
        if os.path.exists(output_local_file):
            os.remove(output_local_file)

    def make_temp_folder(self) -> tempfile.TemporaryDirectory:
        return tempfile.TemporaryDirectory()

    def download_file(self, url, filename, directory=None):
        response = requests.get(
            url,
            verify=os.getenv("REQUESTS_HTTPS_VERIFY", "true").lower() == "true",
            timeout=300,
        )
        response.raise_for_status()

        byte_content = response.content
        if not byte_content:
            raise ValueError("The response content does not exists")

        directory = tempfile.gettempdir() if not directory else directory

        file_path = "{dir}{sep}{filename}".format(
            dir=directory, sep=os.path.sep, filename=filename.lower()
        )
        file = open(file_path, "wb")
        file.write(byte_content)
        file.close()

        if not os.path.isfile(file_path):
            if os.path.isdir(file_path):
                raise IsADirectoryError
            raise Exception(f"The download url {url} not download a valid file")

        return file_path

    def save_file_to_s3(self, client_document, file_name, output_local_file):

        s3_client = boto3.client("s3")

        output_s3_file = f"{client_document}/{file_name}"

        s3_client.upload_file(output_local_file, self.output_bucket, output_s3_file)

        return output_s3_file
