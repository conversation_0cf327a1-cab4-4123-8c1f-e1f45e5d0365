from pb_agro_report_rgca_api.services import CloudRangerService


class SintegraService:
    def __init__(self):
        self.cloudranger = CloudRangerService()

    def get_sintegra_data(self, row):
        status = "Consulta indisponível"
        for state in row["state"]:
            payload = {"document": row["documento"], "state": state}
            try:
                response = self.cloudranger.get_data(
                    "lambda/sefaz-rs/sintegra-unified/document", payload
                )
                if not response.get("records"):
                    status = "NÃO"
                else:
                    status = (
                        "SIM"
                        if response.get("records")[0].get("status")
                        == "Ativo - HABILITADO"
                        else "NÃO"
                    )
            except Exception:
                status = self.get_sintegra_from_ccc(payload)
            if status == "SIM":
                return status
        return status

    def get_sintegra_from_ccc(self, payload):
        try:
            response = self.cloudranger.get_data(
                "lambda/sefaz-rs/sintegra-ccc/document", payload
            )
            if not response.get("records"):
                return "NÃO"

            return (
                "SIM"
                if response.get("records")
                and response.get("records")[0].get("ie_status") == "Habilitado"
                else "NÃO"
            )
        except Exception:
            return "Consulta indisponível"
