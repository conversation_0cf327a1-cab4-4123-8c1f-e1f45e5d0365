from datetime import datetime

from dateutil.relativedelta import relativedelta

from pb_agro_report_rgca_api.services import CloudRangerService


class BasicInfoService:
    def __init__(self):
        self.cloudranger = CloudRangerService()

    def get_basic_info(self, row):
        payload = {"document": row["documento"]}
        url = "lambda/infomais/basic-info/document"
        try:
            response = self.cloudranger.get_data(url, payload).get("records")[0]

            valid_cpf = (
                self.get_valid_cpf(response)
                if len(row["documento"]) == 11
                else "Não se aplica"
            )
            above_21 = (
                self.get_age_above_21(response)
                if len(row["documento"]) == 11
                else "Não se aplica"
            )

            state = self.get_state(response)

            return valid_cpf, above_21, state
        except Exception:
            return (
                "Consulta indisponível",
                "Consulta indisponível",
                "Consulta indisponível",
            )

    def get_state(self, response):
        state = set()
        for address in response.get("addresses"):
            state.add(address.get("uf"))
        return state

    def get_valid_cpf(self, response):
        try:
            return (
                "SIM"
                if response.get("basic_data").get("tax_id_status")
                in ["REGULAR", "ATIVA"]
                else "NÃO"
            )
        except Exception:
            return "Consulta indisponível"

    def get_age_above_21(self, response):
        try:
            birth_date = response.get("basic_data").get("birth_date")
            date_format = "%Y-%m-%d"
            date_obj = datetime.strptime(birth_date, date_format)
            difference_in_years = relativedelta(datetime.now(), date_obj).years

            return "SIM" if difference_in_years >= 21 else "NÃO"
        except Exception:
            return "Consulta indisponível"
