import os

import requests

from pb_agro_report_rgca_api.services.get_iam_token import GetIAMCredentials


class CloudRangerService:
    def __init__(self) -> None:
        self.cloudranger_url = os.getenv("CLOUD_RANGER_BASE_URL")
        self.ssl_verify = os.getenv("REQUESTS_HTTPS_VERIFY", "true").lower() == "true"

    def get_data(self, url, payload):
        iam_service = GetIAMCredentials(
            url=os.getenv("CLOUD_RANGER_LOGIN_URL"),
            username=os.getenv("CLOUD_RANGER_CLIENT_ID"),
            password=os.getenv("CLOUD_RANGER_CLIENT_SECRET"),
        )
        token = iam_service.get_token()
        url = f"{self.cloudranger_url}/{url}"
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json",
        }
        response = requests.post(
            url, headers=headers, json=payload, timeout=60, verify=self.ssl_verify
        )
        response.raise_for_status()
        return response.json()
