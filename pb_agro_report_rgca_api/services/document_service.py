from agri_data_crypto.main import Main
from sqlalchemy import MetaData, Table, select

from pb_agro_report_rgca_api.enums.dataset import Dataset
from pb_agro_report_rgca_api.services.utils_service import UtilsService

TABLES = {
    Dataset.EMBARGOS_SEMA_MT_LISTA: "sema_mt_embargos_a",
    Dataset.EMBARGOS_IBAMA_LISTA: "ibama_embargos_csv_a",
    Dataset.EMBARGOS_LDI_LISTA: "semas_pa_ldi_owners_a",
    Dataset.EMBARGOS_ICMBIO_LISTA: "icmbio_embargos_a",
    Dataset.TRABALHO_ESCRAVO: "mtp_dirty_list_a",
}

COLUMNS = {
    Dataset.EMBARGOS_SEMA_MT_LISTA: "cpf_cnpj",
    Dataset.EMBARGOS_IBAMA_LISTA: "cpf_cnpj",
    Dataset.EMBARGOS_LDI_LISTA: "cpf_cnpj",
    Dataset.EMBARGOS_ICMBIO_LISTA: "cpf_cnpj",
    Dataset.TRABALHO_ESCRAVO: "document",
}


class DocumentService:
    def __init__(self):
        self.utils = UtilsService()
        self.engine = self.utils.get_engine()
        self.metadata = MetaData()
        self.cripter = Main()

    def _encrypt_document(self, document):
        try:
            cleaned_document = self.utils.clean_document(document)
            encrypted_data = self.cripter.encrypt_aes_ctr({"data": cleaned_document})
        except Exception:
            encrypted_data = "-"
        return encrypted_data

    def get_encrypted_docs(self, input_df):
        input_df["encrypted"] = input_df["documento"].apply(
            lambda x: self._encrypt_document(x)
        )
        return input_df, input_df["encrypted"].unique().tolist()

    def is_document_in_list(self, input_df, dataset, documents):
        if TABLES.get(dataset) is None:
            raise ValueError(f"Dataset não permitido: {dataset}")
        document_column = COLUMNS[dataset]
        table_name = TABLES[dataset]
        result = self._execute_stmt(table_name, document_column, documents)
        founded = [row[0] for row in result]
        input_df[dataset.value] = input_df["encrypted"].apply(
            lambda x: self._is_in_list(x, founded)
        )
        return input_df

    def _is_in_list(self, document, founded):
        if document == "-":
            return "-"
        return "SIM" if document in founded else "NÃO"

    def _execute_stmt(self, table_name, document_column, documents):
        table = Table(table_name, self.metadata, autoload_with=self.engine)
        stmt = (
            select(table.c[document_column])
            .where(table.c[document_column].in_(documents))
            .distinct()
        )
        with self.engine.connect() as conn:
            result = conn.execute(stmt).fetchall()
        return result
