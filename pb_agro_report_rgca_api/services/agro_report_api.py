import os

import requests

from pb_agro_report_rgca_api.services.get_iam_token import GetIAMCredentials

DEFAULT_TIMEOUT = 30


class AgroReportApiService:
    def __init__(self, user_id):
        self.user_id = str(user_id)
        self.url = f"{os.getenv('AGRO_REPORT_API_BASE_URL')}/api/v1/"

    def get_headers(self) -> dict:
        auth_service = GetIAMCredentials()
        token = auth_service.get_token()

        return {
            "Authorization": f"Bearer {token}",
            "User-Id": self.user_id,
        }

    def get(self, path, params=None, timeout=DEFAULT_TIMEOUT):
        response = requests.get(
            url=self.url + path,
            params=params,
            headers=self.get_headers(),
            timeout=timeout,
        )
        response.raise_for_status()
        return response

    def post(
        self,
        path,
        params=None,
        data=None,
        json=None,
        timeout=DEFAULT_TIMEOUT,
        files={},
    ):
        response = requests.post(
            url=self.url + path,
            params=params,
            data=data,
            json=json,
            headers=self.get_headers(),
            files=files,
            timeout=timeout,
        )
        response.raise_for_status()
        return response

    def patch(self, path, data=None, json=None, timeout=DEFAULT_TIMEOUT):
        response = requests.patch(
            url=self.url + path,
            data=data,
            json=json,
            headers=self.get_headers(),
            timeout=timeout,
        )
        response.raise_for_status()
        return response

    def delete(self, path, timeout=DEFAULT_TIMEOUT):
        response = requests.delete(
            url=self.url + path, headers=self.get_headers(), timeout=timeout
        )
        response.raise_for_status()
        return response
