from pb_agro_report_rgca_api.services.delivery_api_service import DeliveryApiService


class ProtestsService:
    def __init__(self):
        self.delivery_api_service = DeliveryApiService()

    def get_protests(self, document):
        try:
            if len(document) != 11:
                return "Não se aplica"
            response = self.delivery_api_service.create_and_get_consult(
                document, ["PROTESTS"]
            )
            return "SIM" if response.get("results") else "NÃO"
        except Exception:
            return "Consulta indisponível"
