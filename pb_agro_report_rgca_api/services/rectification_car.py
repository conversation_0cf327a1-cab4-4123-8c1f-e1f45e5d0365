import json
import os

import boto3

session = boto3.Session()


class RectificationCarService:
    def __init__(self):
        region = "sa-east-1"
        self.aws_lambda = os.getenv("AWS_LAMBDA_RECTIFIED_CAR")
        self.lambda_client = session.client("lambda", region_name=region)

    def process_retified_car(self, input_df):
        cars = input_df["car"].unique().tolist()
        if "-" in cars:
            cars.remove("-")
        for car in cars:
            result = self._invoke_lambda(car)
            input_df.loc[input_df["car"] == car, "Retificação CAR"] = result
        input_df.loc[input_df["car"] == "-", "Retificação CAR"] = "-"
        return input_df

    def _invoke_lambda(self, car):
        try:
            response_data = self._invoke_aws_lambda(car)
            qty_items = len(response_data["body"])
            qty_embargoes = sum(
                1
                for item in response_data["body"]
                if "embargos" in item and item["embargos"]
            )
            return "SIM" if qty_items >= 1 and qty_embargoes >= 1 else "NÃO"
        except Exception:
            return "Consulta Indisponível"

    def _invoke_aws_lambda(self, car):
        try:
            payload = {
                "car": car,
                "options": {
                    "returnCarGeometry": False,
                    "returnEmbargoGeometry": False,
                    "returnOverlapGeometry": False,
                },
            }
            json_payload = json.dumps(payload)

            response = self.lambda_client.invoke(
                FunctionName=self.aws_lambda,
                InvocationType="RequestResponse",
                Payload=json_payload,
            )
            response_payload = response["Payload"].read()
            return json.loads(response_payload)
        except Exception:
            return "-"
