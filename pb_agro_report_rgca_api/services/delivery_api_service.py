import os
import time

import requests


class DeliveryApiService:
    def __init__(self) -> None:
        self.delivery_api_url = os.getenv("DELIVERY_API_URL")
        self.delivery_api_email = os.getenv("DELIVERY_API_EMAIL")
        self.delivery_api_password = os.getenv("DELIVERY_API_PASSWORD")
        self.ssl_verify = os.getenv("REQUESTS_HTTPS_VERIFY", "true").lower() == "true"
        self.headers = {"Authorization": f"Bearer {self.get_token()}"}

    def get_token(self):
        """
        Get the token from Delivery API
        @return: Bearer Token
        """
        url = f"{self.delivery_api_url}/auth/login"
        payload = {
            "email": self.delivery_api_email,
            "password": self.delivery_api_password,
        }
        response = requests.post(url, json=payload, timeout=60, verify=self.ssl_verify)
        response.raise_for_status()
        return response.json().get("token")

    def create_consult(self, document, datasets):
        """
        Create a consult in Delivery API
        @param document: CPF or CNPJ
        @param dataset: List of Datasets to be used in the consult
        @return: Consult ID
        """
        url = f"{self.delivery_api_url}/consults"
        payload = {
            "document": document,
            "datasets": datasets,
        }
        response = requests.post(
            url, json=payload, headers=self.headers, timeout=60, verify=self.ssl_verify
        )
        return response.json().get("_id")

    def get_consult(self, consult_id):
        """
        Get the consult results in Delivery API
        @param consult_id: Consult ID
        @return: Consult data
        """
        url = f"{self.delivery_api_url}/consults/{consult_id}"
        response = requests.get(
            url, headers=self.headers, timeout=60, verify=self.ssl_verify
        )
        return response.json().get("datasets")

    def create_and_get_consult(self, document, datasets):
        consult_id = self.create_consult(document, datasets)
        sleep_time, max_retries, retries_count = 5, 6, 0
        while retries_count < max_retries:
            response = self.get_consult(consult_id)[0]
            status = response.get("status")
            if status == "COMPLETED":
                return response
            if status != "PROCESSING":
                return "Consulta indisponível"
            time.sleep(sleep_time)
            retries_count += 1
