import os

import requests

from pb_agro_report_rgca_api.services.get_iam_token import GetIAMCredentials

DEFAULT_TIMEOUT = 30


class AgroRelatedApiService:
    def __init__(self):
        self.url = f"{os.getenv('AGRO_RELATED_API_BASE_URL')}/check"

    def get_headers(self) -> dict:
        auth_service = GetIAMCredentials()
        token = auth_service.get_token()

        return {
            "Authorization": f"Bearer {token}",
        }

    def is_related(self, document: str) -> str:
        try:
            if len(document) != 11:
                return "Não se aplica"

            response = requests.post(
                url=self.url,
                json={"document": document},
                headers=self.get_headers(),
                timeout=DEFAULT_TIMEOUT,
            )
            result = response.json().get("agriRelated")
            if result is True:
                return "SIM"
            elif result is False:
                return "NÃO"
            else:
                return "Consulta indisponível"
        except Exception:
            return "Consulta indisponível"
