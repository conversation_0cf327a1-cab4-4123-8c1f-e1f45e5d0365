import asyncio
import os
import time

import aiohttp
import nest_asyncio

from pb_agro_report_rgca_api.services.get_iam_token import GetIAMCredentials

nest_asyncio.apply()


class AgrowatchService:
    def __init__(self) -> None:
        self.agrowatch_url = os.getenv("AGROWATCH_BASE_URL")
        self.ssl_verify = os.getenv("REQUESTS_HTTPS_VERIFY", "true").lower() == "true"
        self.semaphore = asyncio.Semaphore(50)
        self.token = None
        self.last_token_refresh = 0

    def get_token(self) -> str:
        auth_service = GetIAMCredentials(
            url=os.getenv("AGROWATCH_IAM_URL"),
            username=os.getenv("CLOUD_RANGER_CLIENT_ID"),
            password=os.getenv("CLOUD_RANGER_CLIENT_SECRET"),
        )
        token = auth_service.get_token()
        self.last_token_refresh = time.time()
        return token

    def get_headers(self) -> dict:
        if not self.token or time.time() - self.last_token_refresh > 900:
            self.token = self.get_token()

        return {
            "Authorization": f"Bearer {self.token}",
        }

    async def fetch(self, session, payload, url):
        async with self.semaphore:
            try:
                async with session.post(
                    self.agrowatch_url + url, json=payload, headers=self.get_headers()
                ) as response:
                    response.raise_for_status()
                    return await response.text()
            except aiohttp.ClientError as e:
                return f"Erro na requisição: {e}"

    async def fetch_all(self, payloads, url):
        async with aiohttp.ClientSession() as session:
            tasks = [self.fetch(session, payload, url) for payload in payloads]
            return await asyncio.gather(*tasks)
