from pb_agro_report_rgca_api.services.cloud_ranger_service import CloudRangerService


class BNDESAnalysisService:
    def __init__(self):
        self.verify = False
        self.cloudranger = CloudRangerService()

    def get_bndes(self, doc):
        try:
            payload = {"document": doc}
            response = self.cloudranger.get_data(
                "lambda/bndes/operations/document", payload
            )
            results = response.get("records")
            return "SIM" if len(results) else "NÃO"
        except Exception:
            return "Consulta Indisponível"
