import json
import os

import boto3
import pandas as pd

from pb_agro_report_rgca_api.services.agro_related_api import AgroRelatedApiService
from pb_agro_report_rgca_api.services.basic_info_service import BasicInfoService
from pb_agro_report_rgca_api.services.bndes_service import BNDESAnalysisService
from pb_agro_report_rgca_api.services.protests_service import ProtestsService
from pb_agro_report_rgca_api.services.sintegra_service import SintegraService
from pb_agro_report_rgca_api.services.utils_service import UtilsService

UNAVAILABLE = "Consulta indisponível"
INVALID_DOCUMENT = "Documento Inválido"


class CreditService:
    def __init__(self):
        self.utils_service = UtilsService()
        self.output_bucket = os.getenv("RGCA_BUCKET", "")
        self.agro_score_lambda = (
            "arn:aws:lambda:sa-east-1:492982038605:function:"
            + os.getenv("AGRO_SCORE_LAMBDA", "")
        )
        self.agro_score_lambda_region = os.getenv("AGRO_SCORE_LAMBDA_REGION", "")

    def apply_agro_related(self, input_df):
        agro_related_service = AgroRelatedApiService()
        unique_documents = input_df["documento"].drop_duplicates()
        agro_related_data = unique_documents.apply(agro_related_service.is_related)
        agro_related_df = pd.DataFrame(
            {"documento": unique_documents, "Agro Relacionado": agro_related_data}
        )
        input_df = input_df.merge(agro_related_df, on="documento", how="left")
        return input_df

    def apply_bndes(self, input_df):
        bndes_service = BNDESAnalysisService()
        unique_documents = input_df["documento"].drop_duplicates()
        bndes_data = unique_documents.apply(bndes_service.get_bndes)
        bndes_df = pd.DataFrame(
            {
                "documento": unique_documents,
                "Empréstimos ativos no BNDES": bndes_data,
            }
        )
        input_df = input_df.merge(bndes_df, on="documento", how="left")
        return input_df

    def apply_cndt(self, input_df):
        input_df["Dívidas ativas trabalhistas - CNDT"] = UNAVAILABLE
        return input_df

    def apply_fgts(self, input_df):
        input_df["Dívidas ativas como empregador no FGTS"] = UNAVAILABLE
        return input_df

    def apply_protests(self, input_df):
        try:
            protests_service = ProtestsService()
            unique_documents = input_df["documento"].drop_duplicates()
            protests_data = unique_documents.apply(protests_service.get_protests)
            protests_df = pd.DataFrame(
                {"documento": unique_documents, "Protestos": protests_data}
            )
            input_df = input_df.merge(protests_df, on="documento", how="left")
        except Exception:
            input_df["Protestos"] = UNAVAILABLE

        return input_df

    def apply_federal_revenue(self, input_df):
        input_df["Dívidas ativas na Receita Federal"] = UNAVAILABLE
        return input_df

    def apply_basic_info(self, input_df):
        basic_info_service = BasicInfoService()
        unique_documents = input_df.drop_duplicates(subset=["documento"])

        basic_info_data = unique_documents.apply(
            basic_info_service.get_basic_info, axis=1
        )
        basic_info_df = pd.DataFrame(
            basic_info_data.tolist(),
            columns=["Cadastro de CPF ativo", "Maior de 21 anos", "state"],
        )
        basic_info_df["documento"] = unique_documents["documento"].values
        input_df = input_df.merge(basic_info_df, on="documento", how="left")
        return input_df

    def apply_sintegra(self, input_df):
        sintegra_service = SintegraService()
        unique_documents = input_df.drop_duplicates(subset=["documento"], keep="first")
        sintegra_data = unique_documents.apply(
            sintegra_service.get_sintegra_data, axis=1
        )
        sintegra_df = pd.DataFrame(
            {
                "documento": unique_documents["documento"],
                "Sintegra Ativo": sintegra_data,
            }
        )
        input_df = input_df.merge(sintegra_df, on="documento", how="left")
        return input_df

    def invoke_lambda(self, input_df, requester):
        responses = []

        lambda_client = boto3.client(
            "lambda", region_name=self.agro_score_lambda_region
        )

        documents = input_df["documento"].unique().tolist()

        for document in documents:
            payload = {
                "document": document,
                "requester": requester,
                "re_requery": False,
            }

            json_payload = json.dumps(payload)
            try:
                response = lambda_client.invoke(
                    FunctionName=self.agro_score_lambda,
                    InvocationType="RequestResponse",
                    Payload=json_payload,
                )

                response_payload = response["Payload"].read()

                response_data = json.loads(response_payload)

                response_data["documento"] = document
                responses.append(response_data)
            except Exception as e:
                responses.append({"documento": document, "errorMessage": str(e)})

        responses_df = pd.DataFrame(responses)
        responses_df = responses_df.merge(
            input_df, on="documento", how="left", validate="one_to_many"
        )
        return responses_df.reset_index()

    def apply_business_rules(self, merged_df):
        if "errorMessage" in merged_df:
            expected_mask_error = "The provided document is not a valid CPF or CNPJ"
            invalid_document_mask = merged_df["errorMessage"].str.contains(
                expected_mask_error, na=False
            )
            UtilsService.send_different_error_message_alert(
                merged_df, expected_mask_error
            )

            merged_df.loc[invalid_document_mask, ["score"]] = "Erro"

            merged_df.loc[invalid_document_mask, ["event_probability"]] = (
                INVALID_DOCUMENT
            )

        merged_df.loc[merged_df["score"] == 0, "event_probability"] = "Não Disponível"

        merged_df.loc[merged_df["score"] == 1, "event_probability"] = "Não Disponível"

        merged_df = self.rename_dataframe_columns(merged_df)
        merged_df = self.utils_service.remove_columns(merged_df, ["state"])

        return merged_df

    def rename_dataframe_columns(self, input_df):
        rename_columns = {
            "score": "Agro Score",
            "event_probability": "Probabilidade de Inadimplência",
            "dívida(R$)": "Dívida (R$)",
        }

        for old_name, new_name in rename_columns.items():
            if old_name in input_df.columns:
                input_df = self.utils_service.rename_column(
                    input_df, old_name=old_name, new_name=new_name
                )

        return input_df
