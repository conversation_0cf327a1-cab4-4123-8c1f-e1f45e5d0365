import os

import pandas as pd
from geoalchemy2 import functions as geofunc
from sqlalchemy import MetaData, Table, select
from sqlalchemy.sql import exists

from pb_agro_report_rgca_api.enums.dataset import Dataset
from pb_agro_report_rgca_api.services.utils_service import UtilsService

TABLES = {
    Dataset.ANA_PIVO: "ana_pivos_m",
    Dataset.ARMAZENS: "experian_shippings_m",
    Dataset.EMBARGOS_SEMA_MT: "sema_mt_embargos_a",
    Dataset.EMBARGOS_IBAMA: "ibama_embargos_pamgia_shp_a",
    Dataset.EMBARGOS_LDI: "semas_pa_ldi_shapes_a",
    Dataset.EMBARGOS_ICMBIO: "icmbio_embargos_a",
    Dataset.PRODES: [
        "inpe_prodes_pantanal_a",
        "inpe_prodes_amazonia_a",
        "inpe_prodes_pampa_a",
        "inpe_prodes_caatinga_a",
        "inpe_prodes_cerrado_a",
        "inpe_prodes_mata_atlantica_a",
    ],
    Dataset.QUILOMBOS: "incra_quilombos_a",
    Dataset.TERRAS_INDIGENAS: "funai_indigenous_lands_a",
    Dataset.UNIDADES_CONSERVACAO: "incra_conservation_units_a",
    Dataset.PRA: "sicar_natural_themes_m",
}


class IntersectorService:
    def __init__(self):
        self.utils = UtilsService()
        self.engine = self.utils.get_engine()
        self.private_engine = self.utils.get_engine(
            db_name=os.getenv("DB_NAME_EXPERIAN_AGRI_PRIVATE")
        )
        self.metadata = MetaData()

    def get_pra_intersections(self, input_df: pd.DataFrame):
        car_list = input_df["car"].unique().tolist()
        query = """
            SELECT DISTINCT ON (federal_car_code)
            federal_car_code, pra_membership_request
            FROM sicar_natural_themes_m
            WHERE federal_car_code = ANY(%(car_list)s)
            ORDER BY federal_car_code, id DESC
        """
        with self.engine.connect() as conn:
            pra_df = pd.read_sql(query, conn, params={"car_list": car_list})
            pra_df.replace(
                {r"(?i)^sim$": "SIM", r"(?i)^n[aã]o$": "NÃO"},
                regex=True,
                inplace=True,
            )
            input_df = input_df.merge(
                pra_df,
                how="left",
                left_on="car",
                right_on="federal_car_code",
            )
            input_df = self.utils.remove_columns(
                input_df,
                columns_to_remove=["federal_car_code", "PRA"],
            )
            input_df = self.utils.rename_column(
                input_df, "pra_membership_request", "PRA"
            )
            input_df["PRA"] = input_df["PRA"].fillna("-")
            return input_df

    def get_intersection(self, row, dataset):
        if not TABLES.get(dataset):
            raise ValueError(f"Tabela não permitida: {dataset.value}")

        table_name = TABLES[dataset]
        if isinstance(table_name, list):
            for table in table_name:
                if self._check_intersection(row, table):
                    return "SIM"
            return "NÃO"
        return "SIM" if self._check_intersection(row, table_name) else "NÃO"

    def _check_intersection(self, row, table_name):
        stmt = self._get_stmt(row, table_name)
        return self._execute_stmt(stmt, table_name)

    def _get_stmt(self, row, table_name):
        engine = (
            self.private_engine
            if table_name == TABLES[Dataset.ARMAZENS]
            else self.engine
        )
        table = Table(table_name, self.metadata, autoload_with=engine)
        return select(
            exists(
                select(1)
                .select_from(table)
                .where(
                    geofunc.ST_Intersects(
                        table.c.geom,
                        geofunc.ST_GeomFromText(row["geom"].wkt, 4326),
                    )
                )
                .limit(1)
            )
        )

    def _execute_stmt(self, stmt, table_name):
        engine = (
            self.private_engine
            if table_name == TABLES[Dataset.ARMAZENS]
            else self.engine
        )
        with engine.connect() as conn:
            result = conn.execute(stmt).scalar()
        return result
