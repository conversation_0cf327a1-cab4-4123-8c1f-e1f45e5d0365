import os
import re
from functools import reduce

import pandas as pd
from sqlalchemy import create_engine

from pb_agro_report_rgca_api.enums.dataset import Dataset
from pb_agro_report_rgca_api.services.teams_messages import TeamsMessagesService


class UtilsService:
    def read_input_csv(self, input_csv):
        df = pd.read_csv(
            input_csv,
            sep=";",
            dtype=str,
            encoding="utf-8",
        )
        return df

    def remove_columns(self, df, columns_to_remove):
        df = df.drop(columns=[col for col in columns_to_remove if col in df.columns])
        return df

    def rename_columns(self, df, columns_mapping):
        df = df.rename(columns=columns_mapping)
        return df

    def rename_column(self, df, old_name, new_name):
        df = df.rename(columns={old_name: new_name})
        return df

    def reorder_dataframe(self, df):
        ordered_columns = [
            "Tipo",
            "Documento",
            "CAR",
            "Código Município",
            "Estado",
            "Região",
            "Proprietário1",
            "Proprietário2",
            "Proprietário3",
            "Documento1",
            "Documento2",
            "Documento3",
            "Área total do CAR (ha)",
            "Status CAR",
            "Retificação CAR",
            "Soja (2021/22)",
            "Soja (2022/23)",
            "Soja (2023/24)",
            "Milho (2021/22)",
            "Milho (2022/23)",
            "Milho (2023/24)",
            "Milho 2ª safra (2022/23)",
            "Algodão (2021/22)",
            "Algodão (2022/23)",
            "Algodão (2023/24)",
            "Cana (2021/22)",
            "Cana (2022/23)",
            "Cana (2023/24)",
            "Café (2020/21)",
            "Café (2021/22)",
            "Café (2022/23)",
            "Pasto (2020/21)",
            "Pasto (2021/22)",
            "Pasto (2022/23)",
            "Vegetação nativa (2020/21)",
            "Vegetação nativa (2021/22)",
            "Vegetação nativa (2022/23)",
            Dataset.ANA_PIVO.value,
            Dataset.ARMAZENS.value,
            Dataset.EMBARGOS_IBAMA_LISTA.value,
            Dataset.EMBARGOS_IBAMA.value,
            Dataset.EMBARGOS_SEMA_MT_LISTA.value,
            Dataset.EMBARGOS_SEMA_MT.value,
            Dataset.EMBARGOS_LDI_LISTA.value,
            Dataset.EMBARGOS_LDI.value,
            Dataset.EMBARGOS_ICMBIO_LISTA.value,
            Dataset.EMBARGOS_ICMBIO.value,
            Dataset.TRABALHO_ESCRAVO.value,
            Dataset.PRODES.value,
            Dataset.QUILOMBOS.value,
            Dataset.TERRAS_INDIGENAS.value,
            Dataset.UNIDADES_CONSERVACAO.value,
            Dataset.PRA.value,
            "Agro Relacionado",
            "Agro Score",
            "Probabilidade de Inadimplência",
            "Empréstimos ativos no BNDES",
            "Cadastro de CPF ativo",
            "Maior de 21 anos",
            "Dívidas ativas na Receita Federal",
            "Dívidas ativas trabalhistas - CNDT",
            "Dívidas ativas como empregador no FGTS",
            "Sintegra Ativo",
            "Protestos",
            "Data da Consulta",
        ]

        existing_columns = [col for col in ordered_columns if col in df.columns]

        df = df[existing_columns]

        return df

    def merge_dataframes(self, join_column, dataframe_list):
        if not dataframe_list:
            return pd.DataFrame()
        return reduce(
            lambda left, right: pd.merge(
                left, right, on=join_column, how="outer", validate="many_to_many"
            ),
            dataframe_list,
        )

    @staticmethod
    def send_different_error_message_alert(merged_df, expected_mask_error):
        error_df = merged_df[
            ~merged_df["errorMessage"].isin([expected_mask_error, ""])
            & merged_df["errorMessage"].notna()
        ]

        if not error_df.empty:
            error_message = error_df.to_string(index=False)

            TeamsMessagesService.send_teams_message(
                subject="Agro Score Lambda Unknown Error", message=error_message
            )

    @staticmethod
    def determine_document_type(document):
        cpf_pattern = r"^\d{3}\.\d{3}\.\d{3}-\d{2}$"
        cnpj_pattern = r"^\d{2}\.?\d{3}\.?\d{3}/?\d{4}-?\d{2}$"

        if re.match(cpf_pattern, document):
            return "PF"
        elif re.match(cnpj_pattern, document):
            return "PJ"
        else:
            return "Documento Inválido"

    def remask_document(self, doc):
        if len(doc) == 11:
            return f"{doc[:3]}.xxx.xxx-{doc[9:]}"
        elif len(doc) == 14:
            return f"{doc[:2]}.{doc[2:3]}xx.xxxx/xxxx-{doc[12:]}"
        else:
            return doc

    @staticmethod
    def mask_document(doc):
        if len(doc) == 11:
            return f"{doc[:3]}.{doc[3:6]}.{doc[6:9]}-{doc[9:]}"
        elif len(doc) == 14:
            return f"{doc[:2]}.{doc[2:5]}.{doc[5:8]}/{doc[8:12]}-{doc[12:]}"
        else:
            return doc

    def format_car(self, input_car):
        cleaned_car = re.sub(r"[-.]", "", input_car)

        if len(cleaned_car) == 41:
            formatted_string = re.sub(
                r"([a-zA-Z]{2})(\d{7})([a-zA-Z0-9]{32})", r"\1-\2-\3", cleaned_car
            )
            return formatted_string
        else:
            return input_car

    def clean_document(self, document):
        return re.sub(r"[.\-\/]", "", document)

    def get_engine(self, db_name=None):
        if db_name is None:
            db_name = os.getenv("DB_NAME_EXPERIAN_AGRI_PUBLIC")
        db_host = os.getenv("DB_HOST_EXPERIAN_AGRI_PUBLIC")
        db_port = os.getenv("DB_PORT_EXPERIAN_AGRI_PUBLIC", "5432")
        db_user = os.getenv("DB_USER_EXPERIAN_AGRI_PUBLIC")
        db_password = os.getenv("DB_PASSWORD_EXPERIAN_AGRI_PUBLIC")
        engine = create_engine(
            f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
        )
        return engine

    def get_region_by_state_abbrev(self, state):
        region_mapping = {
            "AC": "Norte",
            "AL": "Nordeste",
            "AP": "Norte",
            "AM": "Norte",
            "BA": "Nordeste",
            "CE": "Nordeste",
            "DF": "Centro-Oeste",
            "ES": "Sudeste",
            "GO": "Centro-Oeste",
            "MA": "Nordeste",
            "MT": "Centro-Oeste",
            "MS": "Centro-Oeste",
            "MG": "Sudeste",
            "PA": "Norte",
            "PB": "Nordeste",
            "PR": "Sul",
            "PE": "Nordeste",
            "PI": "Nordeste",
            "RJ": "Sudeste",
            "RN": "Nordeste",
            "RS": "Sul",
            "RO": "Norte",
            "RR": "Norte",
            "SC": "Sul",
            "SE": "Nordeste",
            "SP": "Sudeste",
            "TO": "Norte",
        }
        return region_mapping.get(state, "-")

    def get_car_situation_description(self, car_situation):
        situation_mapping = {
            "PE": "Pendente",
            "CA": "Cancelado",
            "SU": "Suspenso",
            "AT": "Ativo",
        }
        return situation_mapping.get(car_situation, "")

    def anonymize_name(self, full_name):
        if not isinstance(full_name, str):
            return ""
        name_parts = full_name.split()
        if not name_parts:
            return ""
        if len(name_parts) == 1:
            return name_parts[0]
        first_name = name_parts[0]
        anonymized_rest = " ".join(["*" * len(part) for part in name_parts[1:]])
        return f"{first_name} {anonymized_rest}"

    def get_cars_not_evaluated(self, input_df):

        def extract_cars_not_evaluated(df, col, reason_text):
            result = df.loc[df[col], ["car"]].copy()
            result["reason"] = reason_text
            return result

        cars_invalid = extract_cars_not_evaluated(
            input_df, "car_invalid", "CAR Inválido"
        )
        cars_not_found = extract_cars_not_evaluated(
            input_df, "car_not_found", "CAR Não Encontrado"
        )

        cars_not_evaluated = pd.concat(
            [cars_invalid, cars_not_found], ignore_index=True
        )
        cars_not_evaluated = cars_not_evaluated[["reason", "car"]].rename(
            columns={
                "reason": "Tipo",
                "car": "Dado",
            }
        )

        return cars_not_evaluated

    def car_is_valid(self, car):
        return bool(re.fullmatch(r"^[A-Z]{2}-\d{7}-[A-Z0-9]{32}$", str(car)))
