import json
import os
from datetime import datetime

import requests
from requests.auth import HTTPBasicAuth

TOKEN_IN_MEMORY = {}


class GetIAMCredentials:
    def __init__(self, url=None, username=None, password=None) -> None:
        url_digital_paas = url or os.getenv("URL_DIGITAL_PAAS")
        self.token_url = f"{url_digital_paas}/security/iam/v1/client-identities/login"
        self.username = username or os.getenv("IAM_CLIENT_ID")
        self.password = password or os.getenv("IAM_CLIENT_SECRET")

    def get_token(self):
        now = datetime.now().timestamp()
        if (
            TOKEN_IN_MEMORY.get(self.username)
            and TOKEN_IN_MEMORY.get(self.username).get("expires_at", -1) >= now
        ):
            return TOKEN_IN_MEMORY.get(self.username).get("token")

        headers = {"Content-Type": "application/json"}
        response = requests.post(
            self.token_url,
            headers=headers,
            auth=HTTPBasicAuth(self.username, self.password),
            timeout=30,
        )
        content = json.loads(response.content)
        token = content.get("accessToken")
        TOKEN_IN_MEMORY[self.username] = {"token": token, "expires_at": now + 1800}

        return token
