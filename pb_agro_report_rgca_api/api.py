import os

from fastapi import FastAP<PERSON>
from fastapi_sqlalchemy import DBSessionMiddleware

from . import api_config, routers

api_app = FastAPI(
    version="1.0",
    debug=api_config.debug,
    docs_url="/docs",
    redoc_url=None,
    title="Agro Report RGCA API",
)
DB_USER_APP = os.getenv("DB_USER_APP")
DB_PASSWORD_APP = os.getenv("DB_PASSWORD_APP")
DB_HOST_APP = os.getenv("DB_HOST_APP")
DB_PORT_APP = os.getenv("DB_PORT_APP")
DB_DATABASE_APP = os.getenv("DB_DATABASE_APP")

DB_URL = f"postgresql://{DB_USER_APP}:{DB_PASSWORD_APP}@{DB_HOST_APP}:{DB_PORT_APP}/{DB_DATABASE_APP}"  # noqa
api_app.add_middleware(DBSessionMiddleware, db_url=DB_URL)

PREFIX = "/api"

api_app.include_router(routers.health.router, prefix=PREFIX)
api_app.include_router(routers.report_rgca.router, prefix=PREFIX)
