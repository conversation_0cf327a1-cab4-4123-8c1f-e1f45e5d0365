from celery import Celery

celery_app = Celery("pb_agro_report_rgca_api")
celery_app.config_from_object("pb_agro_report_rgca_api.celery_config")

# CPendingDeprecationWarning: The broker_connection_retry configuration setting will
# no longer determine whether broker connection retries are made during startup in
# Celery 6.0 and above. If you wish to retain the existing behavior for retrying
# connections on startup, you should set broker_connection_retry_on_startup to True.
celery_app.conf.broker_connection_retry_on_startup = True
