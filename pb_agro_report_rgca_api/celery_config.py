import os

from dotenv import load_dotenv
from pb_sm_helper.sm_helper import SmHelper

load_dotenv()

SM_AGRO_REPORT = os.getenv("SM_AGRO_REPORT")

if SM_AGRO_REPORT:
    SmHelper.load_as_env([SM_AGRO_REPORT])


# Celery configs

broker_url = os.getenv("CELERY_BROKER_URL")

if broker_url == "sqs://":
    task_default_queue = "pb-agro-report-rgca-api"

    broker_transport_options = {
        "visibility_timeout": 3600,
        "polling_interval": 1,
        "wait_time_seconds": 10,
        "predefined_queues": {
            task_default_queue: {
                "url": os.getenv("AGRO_REPORT_RGCA_QUEUE_URL"),
            }
        },
    }

result_backend = os.getenv("CELERY_RESULT_BACKEND")

imports = [
    "pb_agro_report_rgca_api.tasks",
]

timezone = "America/Sao_Paulo"
enable_utc = True

result_backend = (
    f"db+postgresql://{os.getenv('DB_USER_APP')}:{os.getenv('DB_PASSWORD_APP')}"
    f"@{os.getenv('DB_HOST_APP')}:{os.getenv('DB_PORT_APP')}"
    f"/{os.getenv('DB_DATABASE_APP')}"
)

result_extended = True
