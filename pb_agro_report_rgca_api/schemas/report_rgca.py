from datetime import datetime
from typing import Optional

from pydantic import BaseModel

from pb_agro_report_rgca_api.enums.report_status import ReportStatus


class ReportRGCACreateOut(BaseModel):
    id: int
    requester: str
    webhook: Optional[str]
    status: ReportStatus
    filename: Optional[str]
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True
