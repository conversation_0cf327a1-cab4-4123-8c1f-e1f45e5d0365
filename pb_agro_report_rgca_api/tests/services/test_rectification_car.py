from unittest.mock import MagicMock, patch

import pandas as pd
import pytest

from pb_agro_report_rgca_api.services.rectification_car import RectificationCarService


@pytest.fixture
def input_df():
    return pd.DataFrame({"car": ["CAR1", "CAR2", "CAR3"]})


@pytest.fixture
def service():
    return RectificationCarService()


@patch.object(RectificationCarService, "_invoke_lambda")
def test_process_retified_car_sets_column(mock_invoke_lambda, service, input_df):
    mock_invoke_lambda.side_effect = ["SIM", "NÃO", "-"]
    result_df = service.process_retified_car(input_df.copy())
    assert list(result_df["Retificação CAR"]) == ["SIM", "NÃO", "-"]


@patch.object(RectificationCarService, "_invoke_aws_lambda")
def test_invoke_lambda_returns_sim(mock_invoke_aws_lambda, service):
    mock_invoke_aws_lambda.return_value = {
        "body": [{"embargos": ["emb1"]}, {"embargos": []}]
    }
    assert service._invoke_lambda("CAR1") == "SIM"


@patch.object(RectificationCarService, "_invoke_aws_lambda")
def test_invoke_lambda_returns_nao(mock_invoke_aws_lambda, service):
    mock_invoke_aws_lambda.return_value = {"body": [{"embargos": []}]}
    assert service._invoke_lambda("CAR2") == "NÃO"


@patch.object(RectificationCarService, "_invoke_aws_lambda")
def test_invoke_lambda_returns_dash_on_exception(mock_invoke_aws_lambda, service):
    mock_invoke_aws_lambda.side_effect = Exception("error")
    assert service._invoke_lambda("CAR3") == "Consulta Indisponível"


@patch("pb_agro_report_rgca_api.services.rectification_car.session")
def test_invoke_aws_lambda_success(mock_session, service):
    mock_lambda_client = MagicMock()
    mock_session.client.return_value = mock_lambda_client
    mock_lambda_client.invoke.return_value = {
        "Payload": MagicMock(
            read=MagicMock(return_value=b'{"body": [{"embargos": ["e"]}]}')
        )
    }
    service.lambda_client = mock_lambda_client
    result = service._invoke_aws_lambda("CAR1")
    assert result == {"body": [{"embargos": ["e"]}]}


def test_invoke_aws_lambda_exception_returns_dash(service):
    service.lambda_client = MagicMock()
    service.lambda_client.invoke.side_effect = Exception("fail")
    assert service._invoke_aws_lambda("CARX") == "-"
