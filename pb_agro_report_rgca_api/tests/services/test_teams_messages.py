import unittest
from unittest.mock import MagicMock, patch

from pb_agro_report_rgca_api.services.teams_messages import TeamsMessagesService


class TestTeamsMessagesService(unittest.TestCase):

    @patch("pb_agro_report_rgca_api.services.teams_messages.pymsteams.connectorcard")
    @patch("pb_agro_report_rgca_api.services.teams_messages.os.getenv")
    def test_send_teams_message(self, mock_getenv, mock_connectorcard):
        mock_getenv.return_value = "fake_webhook_url"
        mock_card = MagicMock()
        mock_connectorcard.return_value = mock_card

        subject = "Test Subject"
        message = "Test Message" * 1000  # Create a long message to test splitting

        TeamsMessagesService.send_teams_message(subject, message)
        mock_getenv.assert_called_once_with("TEAMS_WEBHOOK_RGCA_API", None)
        mock_connectorcard.assert_called_once_with("fake_webhook_url")
        mock_card.title.assert_called_once_with(subject)
        self.assertEqual(
            mock_card.text.call_count, 2
        )  # Message should be split into 2 parts
        self.assertEqual(mock_card.send.call_count, 2)


if __name__ == "__main__":
    unittest.main()
