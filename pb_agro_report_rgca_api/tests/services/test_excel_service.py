import unittest
from unittest.mock import MagicMock, patch

import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import <PERSON>ont, PatternFill

from pb_agro_report_rgca_api.services.excel_service import ExcelService


class TestExcelService(unittest.TestCase):
    def setUp(self):
        self.workbook = Workbook()
        self.sheet = self.workbook.active

    @patch("pb_agro_report_rgca_api.services.excel_service.load_workbook")
    @patch("pb_agro_report_rgca_api.services.excel_service.Image")
    @patch("pandas.DataFrame")
    def test_format_excel_file(self, mock_df, mock_image, mock_load_workbook):
        mock_workbook = MagicMock()
        mock_sheet = MagicMock()
        mock_workbook.__getitem__.return_value = mock_sheet
        mock_load_workbook.return_value = mock_workbook

        service = ExcelService()
        output_local_file = "test_file.xlsx"
        merged_df = mock_df
        merged_df.columns = pd.Index(
            ["Column1", "Dívida (R$)", "Probabilidade de Inadimplência"]
        )

        service.write_data_sheet(output_local_file, merged_df, sub_modules=[])

        mock_load_workbook.assert_called_once_with(output_local_file)
        mock_sheet.add_image.assert_called_once()
        mock_workbook.save.assert_called_once_with(output_local_file)
        mock_workbook.close.assert_called_once()

    @patch("os.path.exists")
    @patch("os.mkdir")
    @patch("builtins.open")
    @patch("pandas.DataFrame.to_excel")
    @patch("pb_agro_report_rgca_api.services.excel_service.datetime")
    def test_save_file_to_excel(
        self, mock_datetime, mock_to_excel, mock_open, mock_mkdir, mock_exists
    ):
        mock_datetime.now.return_value.strftime.return_value = "202301011200"
        mock_exists.return_value = False
        mock_open.return_value = MagicMock()

        service = ExcelService()
        client_document = "test_client"
        merged_df = MagicMock()

        output_local_file, file_name = service.save_file_to_excel(
            client_document, merged_df
        )

        expected_file_name = "rgca_report_202301011200.xlsx"
        expected_output_local_file = (
            f"media/{client_document}/results/{expected_file_name}"
        )

        mock_exists.assert_called_once_with(f"media/{client_document}/results")
        mock_mkdir.assert_called_once_with(f"media/{client_document}/results")
        mock_open.assert_called_once_with(expected_output_local_file, "w")

        self.assertEqual(output_local_file, expected_output_local_file)
        self.assertEqual(file_name, expected_file_name)

    @patch("pb_agro_report_rgca_api.services.excel_service.load_workbook")
    @patch("pb_agro_report_rgca_api.services.excel_service.Image")
    @patch("pandas.DataFrame")
    def test_write_data_sheet(self, mock_df, mock_image, mock_load_workbook):
        mock_workbook = MagicMock()
        mock_sheet = MagicMock()
        mock_workbook.__getitem__.return_value = mock_sheet
        mock_load_workbook.return_value = mock_workbook

        mock_sheet.iter_rows.return_value = [[MagicMock() for _ in range(3)]]
        mock_sheet.columns = [
            [MagicMock(column_letter="A"), MagicMock(column_letter="B")]
        ]
        mock_sheet.max_row = 10

        service = ExcelService()
        output_local_file = "test_file.xlsx"
        merged_df = mock_df
        merged_df.columns = pd.Index(
            ["Column1", "Dívida (R$)", "Probabilidade de Inadimplência"]
        )
        merged_df.shape = (5, 3)
        get_loc_mock = MagicMock()
        get_loc_mock.return_value = 2
        merged_df.columns.get_loc = get_loc_mock

        service.write_data_sheet(output_local_file, merged_df, sub_modules=[])

        mock_load_workbook.assert_called_once_with(output_local_file)
        mock_sheet.add_image.assert_called_once()
        mock_workbook.save.assert_called_once_with(output_local_file)
        mock_workbook.close.assert_called_once()

    @patch("pb_agro_report_rgca_api.services.excel_service.load_workbook")
    @patch("pandas.DataFrame")
    def test_write_notes_sheet(self, mock_df, mock_load_workbook):
        mock_workbook = MagicMock()
        mock_sheet = MagicMock()
        mock_workbook.create_sheet.return_value = mock_sheet
        mock_load_workbook.return_value = mock_workbook

        service = ExcelService()
        output_local_file = "test_file.xlsx"

        service.write_notes_sheet(output_local_file)

        mock_load_workbook.assert_called_once_with(output_local_file)
        mock_workbook.create_sheet.assert_called_once_with("Notas")
        mock_workbook.save.assert_called_once_with(output_local_file)
        mock_workbook.close.assert_called_once()

        # Check if the title cell is set correctly
        mock_sheet.cell.assert_any_call(row=1, column=2, value="Notas")

        # Check if module names are written correctly (column 2)
        mock_sheet.cell.assert_any_call(row=2, column=2, value="Ambiental")
        mock_sheet.cell.assert_any_call(row=4, column=2, value="Agrícola")
        mock_sheet.cell.assert_any_call(row=7, column=2, value="ESG")
        mock_sheet.cell.assert_any_call(row=9, column=2, value="Crédito")
        mock_sheet.cell.assert_any_call(row=20, column=2, value="Observações")

        # Check if note text cells are created (column 3)
        # We don't need to check the exact content since it's handled by rich text
        # Just verify that cells in column 3 are being created
        mock_sheet.cell.assert_any_call(row=2, column=3)
        mock_sheet.cell.assert_any_call(row=3, column=3)
        mock_sheet.cell.assert_any_call(row=4, column=3)

        # Check if additional notes without module name are created
        mock_sheet.cell.assert_any_call(row=21, column=2, value="")
        mock_sheet.cell.assert_any_call(row=22, column=2, value="")

    @patch("os.path.exists")
    @patch("os.mkdir")
    @patch("builtins.open")
    @patch("pandas.DataFrame.to_excel")
    @patch("pb_agro_report_rgca_api.services.excel_service.datetime")
    def test_save_file_to_excel_directory_exists(
        self, mock_datetime, mock_to_excel, mock_open, mock_mkdir, mock_exists
    ):
        mock_datetime.now.return_value.strftime.return_value = "202301011200"
        mock_exists.return_value = True
        mock_open.return_value = MagicMock()

        service = ExcelService()
        client_document = "test_client"
        merged_df = MagicMock()

        output_local_file, file_name = service.save_file_to_excel(
            client_document, merged_df
        )

        expected_file_name = "rgca_report_202301011200.xlsx"
        expected_output_local_file = (
            f"media/{client_document}/results/{expected_file_name}"
        )

        mock_exists.assert_called_once_with(f"media/{client_document}/results")
        mock_mkdir.assert_not_called()
        mock_open.assert_called_once_with(expected_output_local_file, "w")

        self.assertEqual(output_local_file, expected_output_local_file)
        self.assertEqual(file_name, expected_file_name)

    def test_merge_and_style_cells(self):
        start_row = 1
        end_row = 1
        start_column = 1
        end_column = 3
        value = "Test"

        service = ExcelService()
        service.merge_and_style_cells(
            self.sheet,
            start_row,
            end_row,
            start_column,
            end_column,
            value,
            Font(name="Arial", size=12, bold=True),
        )

        merged_cells = list(self.sheet.merged_cells.ranges)
        self.assertEqual(len(merged_cells), 1)
        self.assertEqual(str(merged_cells[0]), "A1:C1")

        cell = self.sheet.cell(row=start_row, column=start_column)
        self.assertEqual(cell.value, value)

        self.assertEqual(cell.font, Font(name="Arial", size=12, bold=True))

    def test_add_created_at_module(self):
        service = ExcelService()
        merged_df = pd.DataFrame(
            {"CAR": [1, 2, 3], "Vegetação nativa (2022/23)": [4, 5, 6]}
        )
        black_fill = PatternFill(
            start_color="000000", end_color="000000", fill_type="solid"
        )
        self.sheet.cell(row=4, column=1, value="Data da Consulta")
        service.add_created_at_module(
            sheet=self.sheet, black_fill=black_fill, merged_df=merged_df
        )

        merged_cells = list(self.sheet.merged_cells.ranges)
        self.assertEqual(len(merged_cells), 1)
        self.assertEqual(str(merged_cells[0]), "A3:A4")

        self.assertEqual(self.sheet.cell(row=3, column=1).value, "Data da Consulta")

        self.assertEqual(self.sheet.cell(row=3, column=1).font.name, "Roboto")
        self.assertEqual(self.sheet.cell(row=3, column=1).font.size, 11)
        self.assertEqual(self.sheet.cell(row=3, column=1).font.color.rgb, "00FFFFFF")

        cell_fill = self.sheet.cell(row=3, column=1).fill
        self.assertEqual(cell_fill.start_color.rgb, "00000000")
        self.assertEqual(cell_fill.end_color.rgb, "00000000")
        self.assertEqual(cell_fill.fill_type, "solid")

    def test_add_module(self):
        service = ExcelService()
        df = pd.DataFrame(
            {"Column1": [1, 2], "Column2": [3, 4], "Column3": [5, 6], "Column4": [7, 8]}
        )

        wb = Workbook()
        sheet = wb.active

        header_font = Font(name="Arial", size=12)
        module_font = Font(name="Arial", size=14)
        module_color = PatternFill(
            start_color="CCCCCC", end_color="CCCCCC", fill_type="solid"
        )

        service.add_module(
            sheet, df, 2, 4, "TEST MODULE", header_font, module_font, module_color
        )

        assert sheet.cell(row=3, column=2).value == "TEST MODULE"
        assert sheet.cell(row=3, column=2).font == module_font
        assert sheet.cell(row=3, column=2).fill == module_color

    def test_add_client_module(self):
        service = ExcelService()
        df = pd.DataFrame(
            {"Column1": [1, 2], "Column2": [3, 4], "CAR": [5, 6], "Documento": [7, 8]}
        )

        wb = Workbook()
        sheet = wb.active

        header_font = Font(name="Arial", size=12)
        module_font = Font(name="Arial", size=14)
        gray_fill = PatternFill(
            start_color="CCCCCC", end_color="CCCCCC", fill_type="solid"
        )

        service.add_client_module(sheet, header_font, module_font, df, gray_fill)

        assert sheet.cell(row=3, column=2).value == "CLIENTE"
        assert sheet.cell(row=3, column=2).font == module_font
        assert sheet.cell(row=3, column=2).fill == gray_fill

    def test_add_module_with_find_columns(self):
        service = ExcelService()
        df = pd.DataFrame(
            {"Column1": [1, 2], "Column2": [3, 4], "Column3": [5, 6], "Column4": [7, 8]}
        )

        wb = Workbook()
        sheet = wb.active

        headers = ["Header1", "Header2", "Header3", "Header4"]
        for col_num, header in enumerate(headers, start=1):
            sheet.cell(row=4, column=col_num, value=header)

        header_font = Font(name="Arial", size=12)
        module_font = Font(name="Arial", size=14)
        module_color = PatternFill(
            start_color="CCCCCC", end_color="CCCCCC", fill_type="solid"
        )

        service.add_module(
            sheet,
            df,
            "Header2",
            "Header4",
            "TEST MODULE",
            header_font,
            module_font,
            module_color,
            columns_to_find=True,
        )

        assert sheet.cell(row=3, column=2).value == "TEST MODULE"
        assert sheet.cell(row=3, column=2).font == module_font
        assert sheet.cell(row=3, column=2).fill == module_color

    @patch("pb_agro_report_rgca_api.services.excel_service.load_workbook")
    def test_write_data_not_evaluated(self, mock_load_workbook):
        mock_workbook = MagicMock()
        mock_sheet = MagicMock()
        mock_workbook.create_sheet.return_value = mock_sheet
        mock_load_workbook.return_value = mock_workbook

        service = ExcelService()
        output_local_file = "test_file.xlsx"

        cars_not_evaluated = pd.DataFrame(
            {"Tipo": ["CAR Inválido", "CAR Não Encontrado"], "Dado": ["CAR2", "CAR1"]}
        )

        service.write_data_not_evaluated(output_local_file, cars_not_evaluated)

        mock_load_workbook.assert_called_once_with(output_local_file)
        mock_workbook.create_sheet.assert_called_once_with("Dados Não Avaliados")
        mock_workbook.save.assert_called_once_with(output_local_file)
        mock_workbook.close.assert_called_once()

        # Check if the title cell is set correctly
        mock_sheet.cell.assert_any_call(
            row=3, column=2, value=cars_not_evaluated.columns[0]
        )
        mock_sheet.cell.assert_any_call(
            row=3, column=3, value=cars_not_evaluated.columns[1]
        )

        # Check if notes are written correctly
        mock_sheet.cell.assert_any_call(
            row=4, column=2, value=cars_not_evaluated.iloc[0]["Tipo"]
        )
        mock_sheet.cell.assert_any_call(
            row=4, column=3, value=cars_not_evaluated.iloc[0]["Dado"]
        )
        mock_sheet.cell.assert_any_call(
            row=5, column=2, value=cars_not_evaluated.iloc[1]["Tipo"]
        )
        mock_sheet.cell.assert_any_call(
            row=5, column=3, value=cars_not_evaluated.iloc[1]["Dado"]
        )


if __name__ == "__main__":
    unittest.main()
