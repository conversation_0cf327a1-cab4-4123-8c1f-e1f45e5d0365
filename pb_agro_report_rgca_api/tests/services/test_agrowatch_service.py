import unittest
from unittest import TestCase
from unittest.mock import MagicMock, patch

import pytest

from pb_agro_report_rgca_api.services.agrowatch_service import AgrowatchService


class TestAgrowatchService(TestCase):
    @patch("pb_agro_report_rgca_api.services.agrowatch_service.GetIAMCredentials")
    @patch("time.time", return_value=1000)
    def test_get_token(self, mock_time, mock_get_iam):
        mock_get_iam.return_value.get_token.return_value = "mock_token"
        service = AgrowatchService()
        token = service.get_token()
        self.assertEqual(token, "mock_token")
        self.assertEqual(service.last_token_refresh, 1000)

    @patch("pb_agro_report_rgca_api.services.agrowatch_service.GetIAMCredentials")
    @patch("time.time", side_effect=[1000, 1000, 2000, 2000])
    def test_get_headers_token_expired(self, mock_time, mock_get_iam):
        mock_get_iam.return_value.get_token.return_value = "mock_token"
        service = AgrowatchService()
        headers = service.get_headers()
        self.assertEqual(headers["Authorization"], "Bearer mock_token")
        self.assertEqual(service.last_token_refresh, 1000)

        headers = service.get_headers()
        self.assertEqual(headers["Authorization"], "Bearer mock_token")
        self.assertEqual(service.last_token_refresh, 1000)

    @patch("pb_agro_report_rgca_api.services.agrowatch_service.GetIAMCredentials")
    @patch("time.time", return_value=1000)
    def test_get_headers_token_valid(self, mock_time, mock_get_iam):
        mock_get_iam.return_value.get_token.return_value = "mock_token"
        service = AgrowatchService()
        headers = service.get_headers()
        self.assertEqual(headers["Authorization"], "Bearer mock_token")
        self.assertEqual(service.last_token_refresh, 1000)

        headers = service.get_headers()
        self.assertEqual(headers["Authorization"], "Bearer mock_token")
        self.assertEqual(service.last_token_refresh, 1000)

    @patch("pb_agro_report_rgca_api.services.agrowatch_service.GetIAMCredentials")
    @patch("aiohttp.ClientSession")
    @pytest.mark.asyncio
    async def test_fetch_(self, mock_client_session, mock_get_iam):
        mock_get_iam.return_value.get_token.return_value = "mock_token"
        mock_session = MagicMock()
        mock_response = MagicMock()
        mock_response.text.return_value = '{"result": "success"}'
        mock_response.raise_for_status.return_value = None
        mock_session.post.return_value = mock_response
        mock_client_session.return_value.__aenter__.return_value = mock_session

        service = AgrowatchService()
        payload = {"key": "value"}
        url = "/test-url"
        response = await service.fetch(mock_session, payload, url)
        self.assertEqual(response, '{"result": "success"}')

    @patch("pb_agro_report_rgca_api.services.agrowatch_service.GetIAMCredentials")
    @patch("aiohttp.ClientSession")
    @pytest.mark.asyncio
    async def test_fetch_all_(self, mock_client_session, mock_get_iam):
        mock_get_iam.return_value.get_token.return_value = "mock_token"
        mock_session = MagicMock()
        mock_response = MagicMock()
        mock_response.text.return_value = '{"result": "success"}'
        mock_response.raise_for_status.return_value = None
        mock_session.post.return_value = mock_response
        mock_client_session.return_value.__aenter__.return_value = mock_session

        service = AgrowatchService()
        payloads = [{"key": "value1"}, {"key": "value2"}]
        url = "/test-url"
        responses = await service.fetch_all(payloads, url)
        self.assertEqual(responses, ['{"result": "success"}', '{"result": "success"}'])


if __name__ == "__main__":
    unittest.main()
