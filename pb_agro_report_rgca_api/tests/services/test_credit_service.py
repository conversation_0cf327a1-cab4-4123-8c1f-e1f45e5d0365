import json
import unittest
from unittest import TestCase
from unittest.mock import MagicMock, patch

import pandas as pd

from pb_agro_report_rgca_api.services.credit_service import CreditService


class TestCreditService(TestCase):
    @patch("pb_agro_report_rgca_api.services.credit_service.UtilsService")
    def setUp(self, mock_utils_service):
        self.mock_utils_service = mock_utils_service.return_value
        self.file_path = "test_file.csv"
        self.service = CreditService()

    @patch(
        "pb_agro_report_rgca_api.services.cloud_ranger_service.CloudRangerService."
        "get_data"
    )
    def test_apply_bndes(self, mock_cloudranger):
        mock_cloudranger.return_value = {"records": ["mocked_value"]}
        df = pd.DataFrame({"documento": ["doc1", "doc2", "doc3"]})
        resultado = self.service.apply_bndes(df)
        self.assertTrue((resultado["Empréstimos ativos no BNDES"] == "SIM").all())

    def test_apply_cndt(self):
        input_df = pd.DataFrame({"documento": ["***********00", "98765432000100"]})
        result = self.service.apply_cndt(input_df)

        self.assertIn("Dívidas ativas trabalhistas - CNDT", result.columns)
        self.assertEqual(
            result["Dívidas ativas trabalhistas - CNDT"].iloc[0],
            "Consulta indisponível",
        )
        self.assertEqual(
            result["Dívidas ativas trabalhistas - CNDT"].iloc[1],
            "Consulta indisponível",
        )

    def test_apply_fgts(self):
        input_df = pd.DataFrame({"documento": ["***********00", "98765432000100"]})
        result = self.service.apply_fgts(input_df)

        self.assertIn("Dívidas ativas como empregador no FGTS", result.columns)
        self.assertEqual(
            result["Dívidas ativas como empregador no FGTS"].iloc[0],
            "Consulta indisponível",
        )
        self.assertEqual(
            result["Dívidas ativas como empregador no FGTS"].iloc[1],
            "Consulta indisponível",
        )

    def test_apply_federal_revenue(self):
        input_df = pd.DataFrame({"documento": ["***********00", "98765432000100"]})
        result = self.service.apply_federal_revenue(input_df)

        self.assertIn("Dívidas ativas na Receita Federal", result.columns)
        self.assertEqual(
            result["Dívidas ativas na Receita Federal"].iloc[0],
            "Consulta indisponível",
        )
        self.assertEqual(
            result["Dívidas ativas na Receita Federal"].iloc[1],
            "Consulta indisponível",
        )

    @patch(
        "pb_agro_report_rgca_api.services.cloud_ranger_service.CloudRangerService."
        "get_data"
    )
    def test_apply_basic_info(self, mock_cloudranger):
        mock_cloudranger.return_value = {
            "records": [
                {
                    "basic_data": {
                        "birth_date": "1990-01-01",
                        "tax_id_status": "ATIVA",
                        "addresses": [{"uf": "RJ"}],
                    }
                }
            ]
        }
        input_df = pd.DataFrame({"documento": ["***********00"]})
        result = self.service.apply_basic_info(input_df)

        self.assertIn("Cadastro de CPF ativo", result.columns)
        self.assertIn("Maior de 21 anos", result.columns)
        self.assertIn("state", result.columns)

    @patch("pb_agro_report_rgca_api.services.credit_service.boto3.client")
    def test_invoke_lambda(self, mock_boto_client):
        mock_lambda_client = mock_boto_client.return_value
        mock_lambda_client.invoke.return_value = {
            "Payload": MagicMock(
                read=MagicMock(
                    return_value=json.dumps(
                        {"result": "success", "document": "***********"}
                    )
                )
            )
        }

        input_df = pd.DataFrame({"documento": ["***********"]})
        requester = "test_requester"
        result_df = self.service.invoke_lambda(input_df, requester)

        self.assertIn("result", result_df.columns)
        self.assertEqual(result_df["result"].iloc[0], "success")

    def test_apply_business_rules(self):
        merged_df = pd.DataFrame(
            {
                "documento": ["***********"],
                "score": [0],
                "errorMessage": ["The provided document is not a valid CPF or CNPJ"],
            }
        )
        service = CreditService()
        result_df = service.apply_business_rules(merged_df)

        self.assertEqual(
            result_df["Probabilidade de Inadimplência"].iloc[0], "Documento Inválido"
        )

    @patch("os.getenv")
    @patch(
        "pb_agro_report_rgca_api.services.delivery_api_service.DeliveryApiService.create_and_get_consult"
    )
    @patch(
        "pb_agro_report_rgca_api.services.delivery_api_service.DeliveryApiService.get_token"
    )
    def test_apply_protests_succesfully(
        self, mock_token, mock_get_consult, mock_getenv
    ):
        mock_getenv.side_effect = lambda key, v=None: f"mock_{key.lower()}"
        mock_get_consult.return_value = {"results": "success"}

        input_df = pd.DataFrame({"documento": ["***********"]})
        result_df = self.service.apply_protests(input_df)

        self.assertIn("Protestos", result_df.columns)
        self.assertEqual(result_df["Protestos"].iloc[0], "SIM")
        self.assertEqual(mock_token.call_count, 1)

    def test_apply_protests_unsuccesfully(self):
        input_df = pd.DataFrame({"documento": ["***********"]})
        result_df = self.service.apply_protests(input_df)

        self.assertIn("Protestos", result_df.columns)
        self.assertEqual(result_df["Protestos"].iloc[0], "Consulta indisponível")

    def test_apply_protests_unsuccesfully_dataframe(self):
        input_df = pd.DataFrame({"documento": ["***********"]})
        result_df = self.service.apply_protests(input_df)

        self.assertIn("Protestos", result_df.columns)
        self.assertEqual(result_df["Protestos"].iloc[0], "Consulta indisponível")

    @patch(
        "pb_agro_report_rgca_api.services.cloud_ranger_service.CloudRangerService."
        "get_data"
    )
    def test_apply_sintegra_status_ativo(self, MockCloudrangerService):
        MockCloudrangerService.return_value = {
            "records": [{"status": "Ativo - HABILITADO"}]
        }

        input_df = pd.DataFrame({"documento": ["***********"], "state": [["RS"]]})
        result_df = self.service.apply_sintegra(input_df)

        self.assertIn("Sintegra Ativo", result_df.columns)
        self.assertEqual(result_df["Sintegra Ativo"].iloc[0], "SIM")

    @patch(
        "pb_agro_report_rgca_api.services.cloud_ranger_service.CloudRangerService."
        "get_data"
    )
    def test_apply_sintegra_status_not_ativo(self, MockCloudrangerService):
        MockCloudrangerService.return_value = {"records": [{"status": ""}]}

        input_df = pd.DataFrame({"documento": ["***********"], "state": [["RS"]]})
        result_df = self.service.apply_sintegra(input_df)

        self.assertIn("Sintegra Ativo", result_df.columns)
        self.assertEqual(result_df["Sintegra Ativo"].iloc[0], "NÃO")

    @patch("pb_agro_report_rgca_api.services.agro_related_api.requests.post")
    def test_apply_agro_related_true(self, MockAgroRelatedApiService):
        mock_response = MagicMock()
        mock_response.content = '{"accessToken": "fake_token"}'
        mock_response.json.return_value = {"agriRelated": True}
        mock_response.raise_for_status = MagicMock()
        MockAgroRelatedApiService.return_value = mock_response

        input_df = pd.DataFrame({"documento": ["***********"]})
        result_df = self.service.apply_agro_related(input_df)

        self.assertIn("Agro Relacionado", result_df.columns)
        self.assertEqual(result_df["Agro Relacionado"].iloc[0], "SIM")

    @patch("pb_agro_report_rgca_api.services.agro_related_api.requests.post")
    def test_apply_agro_related_false(self, MockAgroRelatedApiService):
        mock_response = MagicMock()
        mock_response.content = '{"accessToken": "fake_token"}'
        mock_response.json.return_value = {"agriRelated": False}
        mock_response.raise_for_status = MagicMock()
        MockAgroRelatedApiService.return_value = mock_response

        input_df = pd.DataFrame({"documento": ["***********"]})
        result_df = self.service.apply_agro_related(input_df)

        self.assertIn("Agro Relacionado", result_df.columns)
        self.assertEqual(result_df["Agro Relacionado"].iloc[0], "NÃO")

    @patch("pb_agro_report_rgca_api.services.agro_related_api.requests.post")
    def test_apply_agro_related_error(self, MockAgroRelatedApiService):
        mock_response = MagicMock()
        mock_response.content = '{"accessToken": "fake_token"}'
        mock_response.json.return_value = {}
        mock_response.raise_for_status = MagicMock()
        MockAgroRelatedApiService.return_value = mock_response

        input_df = pd.DataFrame({"documento": ["***********"]})
        result_df = self.service.apply_agro_related(input_df)

        self.assertIn("Agro Relacionado", result_df.columns)
        self.assertEqual(result_df["Agro Relacionado"].iloc[0], "Consulta indisponível")

    def test_rename_dataframe_columns(self):
        responses_df = pd.DataFrame(
            {
                "score": [1],
                "event_probability": [0.5],
                "dívida(R$)": [1000],
            }
        )
        service = CreditService()
        result_df = service.rename_dataframe_columns(responses_df)

        self.assertIn("Agro Score", result_df.columns)
        self.assertIn("Probabilidade de Inadimplência", result_df.columns)
        self.assertIn("Dívida (R$)", result_df.columns)


if __name__ == "__main__":
    unittest.main()
