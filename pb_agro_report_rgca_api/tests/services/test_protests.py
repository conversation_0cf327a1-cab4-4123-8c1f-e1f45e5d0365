import unittest
from unittest.mock import patch

from pb_agro_report_rgca_api.services.protests_service import ProtestsService


class TestProtestsService(unittest.TestCase):
    @patch("pb_agro_report_rgca_api.services.protests_service.DeliveryApiService")
    def setUp(self, mock_delivery_api_service):
        self.mock_delivery_api_service = mock_delivery_api_service.return_value
        self.service = ProtestsService()

    def test_get_protests_returns_sim_for_completed_with_results(self):
        self.mock_delivery_api_service.create_and_get_consult.return_value = {
            "status": "COMPLETED",
            "results": ["12345678910"],
        }
        result = self.service.get_protests("12345678910")
        self.assertEqual(result, "SIM")

    def test_get_protests_returns_nao_for_completed_without_results(self):
        self.mock_delivery_api_service.create_and_get_consult.return_value = {
            "results": []
        }
        result = self.service.get_protests("12345678910")
        self.assertEqual(result, "NÃO")

    def test_get_protests_returns_consulta_indisponivel_for_failed_status(self):
        self.mock_delivery_api_service.create_and_get_consult.return_value = []
        result = self.service.get_protests("12345678910")
        self.assertEqual(result, "Consulta indisponível")

    def test_get_protests_returns_consulta_indisponivel_for_exception(self):
        self.mock_delivery_api_service.create_and_get_consult.side_effect = Exception(
            "Error"
        )
        result = self.service.get_protests("12345678910")
        self.assertEqual(result, "Consulta indisponível")
