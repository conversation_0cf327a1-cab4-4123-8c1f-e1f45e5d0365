from unittest.mock import MagicMock, patch

import pandas as pd
import pytest

from pb_agro_report_rgca_api.enums.dataset import Dataset
from pb_agro_report_rgca_api.services.document_service import DocumentService


@pytest.fixture
def document_service():
    with patch(
        "pb_agro_report_rgca_api.services.document_service.UtilsService"
    ) as MockUtilsService:
        mock_utils = MockUtilsService.return_value
        mock_utils.get_engine.return_value = MagicMock()
        mock_utils.clean_document.side_effect = lambda x: x
        mock_utils.remove_columns.side_effect = lambda df, cols: df.drop(columns=cols)
        with patch(
            "pb_agro_report_rgca_api.services.document_service.Main"
        ) as MockMain:
            mock_cripter = MockMain.return_value
            mock_cripter.encrypt_aes_ctr.side_effect = lambda d: f"enc_{d['data']}"
            yield DocumentService()


def test_encrypt_document(document_service):
    df = pd.DataFrame({"documento": ["123", "456"]})
    df, result = document_service.get_encrypted_docs(df.copy())
    assert "encrypted" in df.columns
    assert result == ["enc_123", "enc_456"]


@patch.object(DocumentService, "_execute_stmt")
def test_is_document_in_list_found(mock_execute_stmt, document_service):
    df = pd.DataFrame(
        {"documento": ["123", "456"], "encrypted": ["enc_123", "enc_456"]}
    )
    dataset = Dataset.EMBARGOS_SEMA_MT_LISTA
    mock_execute_stmt.return_value = [("enc_123",)]
    result = document_service.is_document_in_list(
        df.copy(), dataset, ["enc_123", "enc_456"]
    )
    assert dataset.value in result.columns
    assert result.loc[result["documento"] == "123", dataset.value].iloc[0] == "SIM"
    assert result.loc[result["documento"] == "456", dataset.value].iloc[0] == "NÃO"


@patch.object(DocumentService, "_execute_stmt")
def test_is_document_in_list_not_found(mock_execute_stmt, document_service):
    df = pd.DataFrame({"documento": ["789"], "encrypted": ["enc_789"]})
    dataset = Dataset.EMBARGOS_IBAMA_LISTA
    mock_execute_stmt.return_value = []
    result = document_service.is_document_in_list(df.copy(), dataset, ["enc_789"])
    assert result[dataset.value].iloc[0] == "NÃO"


def test_is_document_in_list_invalid_dataset(document_service):
    df = pd.DataFrame({"documento": ["123"], "encrypted": ["enc_123"]})
    with pytest.raises(ValueError):
        document_service.is_document_in_list(df, "INVALID_DATASET", ["enc_123"])


@patch("pb_agro_report_rgca_api.services.document_service.Main")
@patch("pb_agro_report_rgca_api.services.document_service.UtilsService")
def test_encrypt_document_success(mock_utils_cls, mock_main_cls):
    # Arrange
    mock_utils = mock_utils_cls.return_value
    mock_utils.clean_document.side_effect = lambda x: f"clean_{x}"
    mock_main = mock_main_cls.return_value
    mock_main.encrypt_aes_ctr.side_effect = lambda d: f"enc_{d['data']}"
    service = DocumentService()
    # Act
    result = service._encrypt_document("123")
    # Assert
    assert result == "enc_clean_123"


@patch("pb_agro_report_rgca_api.services.document_service.Main")
@patch("pb_agro_report_rgca_api.services.document_service.UtilsService")
def test_encrypt_document_exception_returns_dash(mock_utils_cls, mock_main_cls):
    # Arrange
    mock_utils = mock_utils_cls.return_value
    mock_utils.clean_document.side_effect = Exception("fail")
    service = DocumentService()
    # Act
    result = service._encrypt_document("123")
    # Assert
    assert result == "-"


@patch("pb_agro_report_rgca_api.services.document_service.Main")
@patch("pb_agro_report_rgca_api.services.document_service.UtilsService")
def test_get_encrypted_docs_applies_encryption(mock_utils_cls, mock_main_cls):
    # Arrange
    mock_utils = mock_utils_cls.return_value
    mock_utils.clean_document.side_effect = lambda x: x
    mock_main = mock_main_cls.return_value
    mock_main.encrypt_aes_ctr.side_effect = lambda d: f"enc_{d['data']}"
    service = DocumentService()
    df = pd.DataFrame({"documento": ["abc", "def", "abc"]})
    # Act
    result_df, unique_enc = service.get_encrypted_docs(df.copy())
    # Assert
    assert "encrypted" in result_df.columns
    assert set(result_df["encrypted"]) == {"enc_abc", "enc_def"}
    assert set(unique_enc) == {"enc_abc", "enc_def"}
