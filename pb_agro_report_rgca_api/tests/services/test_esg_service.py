from unittest.mock import MagicMock

import pandas as pd
import pytest

from pb_agro_report_rgca_api.services.esg_service import ESGService


@pytest.fixture
def sample_input_df():
    return pd.DataFrame(
        [
            {
                "car": "CAR123",
                "geom": "geom1",
                "documento": "11111111111",
                "document": "11111111111",
            },
            {
                "car": "CAR456",
                "geom": "geom2",
                "documento": "11111111111",
                "document": "11111111111",
            },
            {
                "car": "CAR789",
                "geom": None,
                "documento": "11111111111",
                "document": "11111111111",
            },
        ]
    )


def test_process_esg_intersections_calls_get_intersection(sample_input_df):
    service = ESGService()
    service.intersector.get_pra_intersections = MagicMock(
        return_value=sample_input_df.copy()
    )
    service.intersector.get_intersection = MagicMock(return_value="YES")
    service.document_service.is_document_in_list = MagicMock(
        return_value=sample_input_df.copy()
    )

    service.process_esg_intersections(sample_input_df.copy())

    # Should be called for each dataset and each row with geom not None
    expected_calls = 2 * 8  # 2 rows with geom, 8 datasets
    assert service.intersector.get_intersection.call_count == expected_calls
