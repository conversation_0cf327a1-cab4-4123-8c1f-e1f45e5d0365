import unittest
from unittest.mock import MagicMock, patch

from pb_agro_report_rgca_api.services.agro_related_api import AgroRelatedApiService


class TestAgroRelatedApiService(unittest.TestCase):

    @patch("pb_agro_report_rgca_api.services.agro_related_api.os.getenv")
    def setUp(self, mock_getenv):
        mock_getenv.return_value = "http://mockurl.com"
        self.service = AgroRelatedApiService()

    @patch("pb_agro_report_rgca_api.services.agro_related_api.GetIAMCredentials")
    def test_get_headers(self, mock_get_iam_credentials):
        mock_token = "mock_token"
        mock_auth_service = mock_get_iam_credentials.return_value
        mock_auth_service.get_token.return_value = mock_token

        headers = self.service.get_headers()

        self.assertEqual(headers, {"Authorization": f"Bearer {mock_token}"})

    @patch("pb_agro_report_rgca_api.services.agro_related_api.requests.post")
    @patch(
        "pb_agro_report_rgca_api.services.agro_related_api.AgroRelatedApiService."
        "get_headers"
    )
    def test_is_related_sim(self, mock_get_headers, mock_post):
        mock_get_headers.return_value = {"Authorization": "Bearer mock_token"}
        mock_response = MagicMock()
        mock_response.json.return_value = {"agriRelated": True}
        mock_post.return_value = mock_response

        result = self.service.is_related("12345678910")

        self.assertEqual(result, "SIM")

    @patch("pb_agro_report_rgca_api.services.agro_related_api.requests.post")
    @patch(
        "pb_agro_report_rgca_api.services.agro_related_api.AgroRelatedApiService."
        "get_headers"
    )
    def test_is_related_nao(self, mock_get_headers, mock_post):
        mock_get_headers.return_value = {"Authorization": "Bearer mock_token"}
        mock_response = MagicMock()
        mock_response.json.return_value = {"agriRelated": False}
        mock_post.return_value = mock_response

        result = self.service.is_related("12345678910")

        self.assertEqual(result, "NÃO")

    @patch("pb_agro_report_rgca_api.services.agro_related_api.requests.post")
    @patch(
        "pb_agro_report_rgca_api.services.agro_related_api.AgroRelatedApiService."
        "get_headers"
    )
    def test_is_related_unavailable(self, mock_get_headers, mock_post):
        mock_get_headers.return_value = {"Authorization": "Bearer mock_token"}
        mock_response = MagicMock()
        mock_response.json.return_value = {"agriRelated": None}
        mock_post.return_value = mock_response

        result = self.service.is_related("12345678910")

        self.assertEqual(result, "Consulta indisponível")

    @patch("pb_agro_report_rgca_api.services.agro_related_api.requests.post")
    @patch(
        "pb_agro_report_rgca_api.services.agro_related_api.AgroRelatedApiService."
        "get_headers"
    )
    def test_is_related_exception(self, mock_get_headers, mock_post):
        mock_get_headers.return_value = {"Authorization": "Bearer mock_token"}
        mock_post.side_effect = Exception("Mock exception")

        result = self.service.is_related("12345678910")

        self.assertEqual(result, "Consulta indisponível")


if __name__ == "__main__":
    unittest.main()
