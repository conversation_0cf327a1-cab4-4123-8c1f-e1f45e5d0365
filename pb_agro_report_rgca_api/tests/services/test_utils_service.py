import unittest
from unittest.mock import patch

import pandas as pd

from pb_agro_report_rgca_api.services.utils_service import UtilsService


class TestUtilsService(unittest.TestCase):

    def setUp(self):
        self.utils_service = UtilsService()

    def test_remove_columns(self):
        df = pd.DataFrame({"A": [1, 2, 3], "B": [4, 5, 6], "C": [7, 8, 9]})
        columns_to_remove = ["B", "C"]
        result_df = self.utils_service.remove_columns(df, columns_to_remove)
        expected_df = pd.DataFrame({"A": [1, 2, 3]})
        pd.testing.assert_frame_equal(result_df, expected_df)

    def test_rename_column(self):
        df = pd.DataFrame({"A": [1, 2, 3], "B": [4, 5, 6]})
        result_df = self.utils_service.rename_column(df, "A", "X")
        expected_df = pd.DataFrame({"X": [1, 2, 3], "B": [4, 5, 6]})
        pd.testing.assert_frame_equal(result_df, expected_df)

    def test_rename_columns(self):
        df = pd.DataFrame({"A": [1, 2, 3], "B": [4, 5, 6]})
        result_df = self.utils_service.rename_columns(df, {"A": "X", "B": "Y"})
        expected_df = pd.DataFrame({"X": [1, 2, 3], "Y": [4, 5, 6]})
        pd.testing.assert_frame_equal(result_df, expected_df)

    def test_reorder_dataframe(self):
        df = pd.DataFrame(
            {
                "Tipo": ["A", "B"],
                "Documento": ["123", "456"],
                "Dívida (R$)": [100, 200],
                "Score de Crédito": [700, 800],
                "Agro Score": [10, 20],
                "Probabilidade de Inadimplência": [0.1, 0.2],
                "Data da Consulta": ["20/03/2025", "20/03/2025"],
                "Extra Column": ["extra1", "extra2"],
                "Empréstimos ativos no BNDES": ["SIM", "NÃO"],
                "Agro Relacionado": ["SIM", "NÃO"],
                "Cadastro de CPF ativo": ["SIM", "NÃO"],
                "Maior de 21 anos": ["SIM", "NÃO"],
                "Sintegra Ativo": ["SIM", "NÃO"],
                "Dívidas ativas na Receita Federal": ["SIM", "NÃO"],
                "Dívidas ativas como empregador no FGTS": ["SIM", "NÃO"],
                "Dívidas ativas trabalhistas - CNDT": ["SIM", "NÃO"],
                "Protestos": ["SIM", "NÃO"],
            }
        )
        result_df = self.utils_service.reorder_dataframe(df)
        expected_df = pd.DataFrame(
            {
                "Tipo": ["A", "B"],
                "Documento": ["123", "456"],
                "Agro Relacionado": ["SIM", "NÃO"],
                "Agro Score": [10, 20],
                "Probabilidade de Inadimplência": [0.1, 0.2],
                "Empréstimos ativos no BNDES": ["SIM", "NÃO"],
                "Cadastro de CPF ativo": ["SIM", "NÃO"],
                "Maior de 21 anos": ["SIM", "NÃO"],
                "Dívidas ativas na Receita Federal": ["SIM", "NÃO"],
                "Dívidas ativas trabalhistas - CNDT": ["SIM", "NÃO"],
                "Dívidas ativas como empregador no FGTS": ["SIM", "NÃO"],
                "Sintegra Ativo": ["SIM", "NÃO"],
                "Protestos": ["SIM", "NÃO"],
                "Data da Consulta": ["20/03/2025", "20/03/2025"],
            }
        )
        pd.testing.assert_frame_equal(result_df, expected_df)

    def test_merge_dataframes(self):
        df1 = pd.DataFrame({"key": ["A", "B"], "value1": [1, 2]})
        df2 = pd.DataFrame({"key": ["A", "C"], "value2": [3, 4]})
        result_df = self.utils_service.merge_dataframes("key", [df1, df2])
        expected_df = pd.DataFrame(
            {
                "key": ["A", "B", "C"],
                "value1": [1, 2, None],
                "value2": [3, None, 4],
            }
        )
        pd.testing.assert_frame_equal(result_df, expected_df)

    def test_merge_dataframes_empty_df(self):
        df_list = []
        result_df = self.utils_service.merge_dataframes("key", df_list)
        expected_df = pd.DataFrame()
        pd.testing.assert_frame_equal(result_df, expected_df)

    @patch("pb_agro_report_rgca_api.services.utils_service.TeamsMessagesService")
    def test_send_different_error_message_alert(self, mock_teams_service):
        expected_mask_error = "The provided document is not a valid CPF or CNPJ"
        merged_df = pd.DataFrame(
            {"errorMessage": ["error1", "error2", "error3"], "id": [1, 2, 3]}
        )
        self.utils_service.send_different_error_message_alert(
            merged_df, expected_mask_error
        )
        mock_teams_service.send_teams_message.assert_called()

    def test_determine_document_type(self):
        self.assertEqual(
            self.utils_service.determine_document_type("123.456.789-00"), "PF"
        )
        self.assertEqual(
            self.utils_service.determine_document_type("12.345.678/0001-00"), "PJ"
        )
        self.assertEqual(
            self.utils_service.determine_document_type("invalid"),
            "Documento Inválido",
        )

    def test_mask_document(self):
        self.assertEqual(
            self.utils_service.mask_document("12345678900"), "123.456.789-00"
        )
        self.assertEqual(
            self.utils_service.mask_document("12345678000100"),
            "12.345.678/0001-00",
        )
        self.assertEqual(self.utils_service.mask_document("invalid"), "invalid")

    def test_format_car_valid(self):
        service = UtilsService()
        input_car = "BA-2902708-24AF382EBA6042C8B68B2FFBA195FF31"
        expected_output = "BA-2902708-24AF382EBA6042C8B68B2FFBA195FF31"
        self.assertEqual(service.format_car(input_car), expected_output)

    def test_format_car_invalid_length(self):
        service = UtilsService()
        input_car = "AB1234567CDEFGHIJKLMNOPQRSTUVWXYZ123456789"
        self.assertEqual(
            service.format_car(input_car), "AB1234567CDEFGHIJKLMNOPQRSTUVWXYZ123456789"
        )

    def test_format_car_invalid_format(self):
        service = UtilsService()
        input_car = "1234567CDEFGHIJKLMNOPQRSTUVWXYZ1234567890"
        self.assertEqual(service.format_car(input_car), input_car)

    def test_clean_document_removes_dots_hyphens_slashes(self):
        self.assertEqual(
            self.utils_service.clean_document("123.456.789-00"), "12345678900"
        )
        self.assertEqual(
            self.utils_service.clean_document("12.345/6789-00"), "12345678900"
        )
        self.assertEqual(
            self.utils_service.clean_document("111/222.333-44"), "11122233344"
        )

    def test_clean_document_with_no_special_chars(self):
        self.assertEqual(
            self.utils_service.clean_document("12345678900"), "12345678900"
        )

    def test_clean_document_empty_string(self):
        self.assertEqual(self.utils_service.clean_document(""), "")

    @patch("pb_agro_report_rgca_api.services.utils_service.create_engine")
    @patch.dict(
        "os.environ",
        {
            "DB_HOST_EXPERIAN_AGRI_PUBLIC": "localhost",
            "DB_NAME_EXPERIAN_AGRI_PUBLIC": "testdb",
            "DB_PORT_EXPERIAN_AGRI_PUBLIC": "5432",
            "DB_USER_EXPERIAN_AGRI_PUBLIC": "testuser",
            "DB_PASSWORD_EXPERIAN_AGRI_PUBLIC": "testpass",
        },
    )
    def test_get_engine_success(self, mock_create_engine):
        mock_engine = object()
        mock_create_engine.return_value = mock_engine

        engine = self.utils_service.get_engine()

        expected_url = "postgresql://testuser:testpass@localhost:5432/testdb"
        mock_create_engine.assert_called_once_with(expected_url)
        self.assertIs(engine, mock_engine)

    @patch("pb_agro_report_rgca_api.services.utils_service.create_engine")
    @patch.dict("os.environ", {}, clear=True)
    def test_get_engine_missing_env_vars(self, mock_create_engine):
        # All env vars missing, so db_user, db_password, etc. will be None
        self.utils_service.get_engine()
        expected_url = "********************************/None"
        mock_create_engine.assert_called_once_with(expected_url)

    def test_get_cars_not_evaluated_empty_df(self):
        df = pd.DataFrame(columns=["car", "car_invalid", "car_not_found"])
        result_df = self.utils_service.get_cars_not_evaluated(df)
        expected_df = pd.DataFrame(columns=["Tipo", "Dado"])
        pd.testing.assert_frame_equal(result_df, expected_df)

    def test_get_cars_not_evaluated(self):
        df = pd.DataFrame(
            {
                "car": ["CAR1", "CAR2", "CAR3", "CAR4"],
                "car_invalid": [False, True, False, False],
                "car_not_found": [True, False, True, False],
            }
        )
        result_df = self.utils_service.get_cars_not_evaluated(df)

        expected_df = pd.DataFrame(
            {
                "Tipo": ["CAR Inválido", "CAR Não Encontrado", "CAR Não Encontrado"],
                "Dado": ["CAR2", "CAR1", "CAR3"],
            }
        )

        pd.testing.assert_frame_equal(result_df, expected_df)

    def test_get_cars_not_evaluated_all_valid(self):
        df = pd.DataFrame(
            {
                "car": ["CAR1", "CAR2", "CAR3"],
                "car_invalid": [False, False, False],
                "car_not_found": [False, False, False],
            }
        )
        result_df = self.utils_service.get_cars_not_evaluated(df)
        expected_df = pd.DataFrame(columns=["Tipo", "Dado"])
        pd.testing.assert_frame_equal(result_df, expected_df)

    def test_car_is_valid_length(self):
        empty_car = ""
        invalid_car_extra_state = f"A-{'0' * 7}-{'A' * 32}"
        invalid_car_missing_state = f"AMA-{'0' * 7}-{'A' * 32}"
        invalid_car_extra_digit = f"AM-{'0' * 6}-{'A' * 32}"
        invalid_car_missing_digit = f"AM-{'0' * 8}-{'A' * 32}"
        invalid_car_extra_char = f"AM-{'0' * 7}-{'A' * 31}"
        invalid_car_missing_char = f"AM-{'0' * 7}-{'A' * 33}"
        valid_car = f"AM-{'0' * 7}-{'A' * 32}"

        self.assertFalse(self.utils_service.car_is_valid(empty_car))
        self.assertFalse(self.utils_service.car_is_valid(invalid_car_extra_state))
        self.assertFalse(self.utils_service.car_is_valid(invalid_car_missing_state))
        self.assertFalse(self.utils_service.car_is_valid(invalid_car_extra_digit))
        self.assertFalse(self.utils_service.car_is_valid(invalid_car_missing_digit))
        self.assertFalse(self.utils_service.car_is_valid(invalid_car_extra_char))
        self.assertFalse(self.utils_service.car_is_valid(invalid_car_missing_char))
        self.assertTrue(self.utils_service.car_is_valid(valid_car))


if __name__ == "__main__":
    unittest.main()
