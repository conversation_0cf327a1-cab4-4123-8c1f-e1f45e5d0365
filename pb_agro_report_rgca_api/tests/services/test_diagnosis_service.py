import json
import unittest
from unittest import TestCase
from unittest.mock import MagicMock, patch

import pandas as pd

from pb_agro_report_rgca_api.enums.sub_modules import SubModules
from pb_agro_report_rgca_api.services.diagnosis_service import DiagnosisService


class TestDiagnosisService(TestCase):
    @patch("pb_agro_report_rgca_api.services.diagnosis_service.UtilsService")
    @patch("pb_agro_report_rgca_api.services.diagnosis_service.AgrowatchService")
    def setUp(self, mock_agrowatch_api_service, mock_utils_service):
        self.mock_agrowatch_api_service = mock_agrowatch_api_service.return_value
        self.mock_utils_service = mock_utils_service.return_value
        self.file_path = "test_file.csv"
        self.service = DiagnosisService()

    @patch("pb_agro_report_rgca_api.services.diagnosis_service.SoilUseService")
    @patch("pb_agro_report_rgca_api.services.diagnosis_service.UtilsService")
    def test_apply_soil_use(self, mock_UtilsService, mock_SoilUseService):
        mock_utils_service = mock_UtilsService.return_value
        mock_soil_use_service = mock_SoilUseService.return_value

        mock_utils_service.format_car.side_effect = (
            lambda input_car: f"formatted_{input_car}"
        )
        mock_soil_use_service.get_soil_use.return_value = {
            "formatted_car1": {"column1": "value1", "column2": "value2"},
            "formatted_car2": {"column1": "value3", "column2": "value4"},
        }

        mock_soil_use_service.check_landmarks.side_effect = lambda x: x

        input_df = pd.DataFrame({"car": ["car1", "car2", "car3"]})

        agro_score_service = DiagnosisService()
        result_df = agro_score_service._apply_soil_use(input_df)

        expected_columns = ["car", "column1", "column2"]
        for col in expected_columns:
            if col not in result_df.columns:
                result_df[col] = pd.NA

        result_df = result_df.fillna("")

        self.assertEqual(result_df.at[2, "column1"], "")
        self.assertEqual(result_df.at[2, "column2"], "")

    def test_get_agrowatch_payload(self):
        self.mock_utils_service.clean_document.return_value = "cleaned_doc"

        input_df = pd.DataFrame({"documento": ["doc1", "doc2"]})
        payloads, documents = self.service.get_agrowatch_payload(input_df)

        expected_payloads = [
            {"document": "cleaned_doc", "source": "federal"},
            {"document": "cleaned_doc", "source": "federal"},
        ]
        expected_documents = ["doc1", "doc2"]

        self.assertEqual(payloads, expected_payloads)
        self.assertEqual(documents, expected_documents)

    @patch("asyncio.get_event_loop")
    def test_search_car_in_agrowatch_api(self, mock_get_event_loop):
        self.mock_utils_service.clean_document.return_value = lambda x: x

        mock_loop = MagicMock()
        mock_loop.run_until_complete.return_value = [
            json.dumps({"records": [{"federal_car_code": "123"}]}),
            json.dumps({"records": []}),
            json.dumps({"records": {}}),
        ]
        mock_get_event_loop.return_value = mock_loop

        input_df = pd.DataFrame({"documento": ["doc1", "doc2", "doc3"]})
        result_df = self.service.search_car_in_agrowatch_api(input_df)

        expected_df = pd.DataFrame(
            [
                {"documento": "doc1", "car": "123"},
                {"documento": "doc2", "car": "-"},
                {"documento": "doc3", "car": "Consulta indisponível"},
            ]
        )

        pd.testing.assert_frame_equal(result_df, expected_df)

    @patch("pb_agro_report_rgca_api.services.diagnosis_service.ExcelService")
    @patch("pb_agro_report_rgca_api.services.diagnosis_service.FileService")
    @patch("pb_agro_report_rgca_api.services.diagnosis_service.UtilsService")
    def test_generate_report_credit_only(
        self,
        mock_UtilsService,
        mock_FileService,
        mock_ExcelService,
    ):
        mock_utils = mock_UtilsService.return_value
        mock_excel = mock_ExcelService.return_value
        mock_file = mock_FileService.return_value

        # Prepare input DataFrame
        input_df = pd.DataFrame({"documento": ["doc1"]})
        mock_utils.read_input_csv.return_value = input_df.copy()
        mock_excel.save_file_to_excel.return_value = (
            "local_path.xlsx",
            "file_name.xlsx",
        )
        mock_file.save_file_to_s3.return_value = "s3_path.xlsx"

        # Patch _apply_credit to just return the df
        with patch.object(
            DiagnosisService, "_apply_credit", return_value=input_df.copy()
        ) as mock_apply_credit:
            service = DiagnosisService()
            result = service.generate_report(
                "fake_path.csv", [SubModules.CREDIT.value], "client_doc"
            )

        assert result["output_local_file"] == "local_path.xlsx"
        assert result["output_s3_file"] == "s3_path.xlsx"
        mock_utils.read_input_csv.assert_called_once()
        mock_excel.save_file_to_excel.assert_called_once()
        mock_file.save_file_to_s3.assert_called_once()
        mock_apply_credit.assert_called_once()

    @patch("pb_agro_report_rgca_api.services.diagnosis_service.ExcelService")
    @patch("pb_agro_report_rgca_api.services.diagnosis_service.FileService")
    @patch("pb_agro_report_rgca_api.services.diagnosis_service.UtilsService")
    @patch("pb_agro_report_rgca_api.services.diagnosis_service.AgrowatchService")
    @patch("pb_agro_report_rgca_api.services.diagnosis_service.SubModules")
    def test_generate_report_with_car_lookup_and_soil_use(
        self,
        mock_SubModules,
        mock_AgrowatchService,
        mock_UtilsService,
        mock_FileService,
        mock_ExcelService,
    ):
        # Setup SubModules enum values
        mock_SubModules.CREDIT.value = "credit"
        mock_SubModules.AGRICULTURAL.value = "agricultural"
        mock_SubModules.LAND.value = "land"
        mock_SubModules.ESG.value = "esg"

        mock_utils = mock_UtilsService.return_value
        mock_excel = mock_ExcelService.return_value
        mock_file = mock_FileService.return_value

        # DataFrame missing 'car', so search_car_in_agrowatch_api will be called
        input_df = pd.DataFrame({"documento": ["doc1"]})
        after_car_df = pd.DataFrame({"documento": ["doc1"], "car": ["car1"]})
        after_geom_df = after_car_df.copy()
        mock_utils.read_input_csv.return_value = input_df.copy()

        # Patch search_car_in_agrowatch_api and get_car_geometries
        with patch.object(
            DiagnosisService,
            "search_car_in_agrowatch_api",
            return_value=after_car_df.copy(),
        ) as mock_search_car, patch.object(
            DiagnosisService,
            "get_car_geometries",
            return_value=after_geom_df.copy(),
        ) as mock_get_geom, patch.object(
            DiagnosisService, "_apply_soil_use", return_value=after_geom_df.copy()
        ) as mock_apply_soil_use:

            mock_utils.remove_columns.side_effect = lambda df, cols: df.drop(
                columns=[c for c in cols if c in df.columns], errors="ignore"
            )
            mock_utils.reorder_dataframe.side_effect = lambda df: df
            mock_excel.save_file_to_excel.return_value = (
                "local_path.xlsx",
                "file_name.xlsx",
            )
            mock_file.save_file_to_s3.return_value = "s3_path.xlsx"

            service = DiagnosisService()
            result = service.generate_report(
                "fake_path.csv", ["agricultural"], "client_doc"
            )

        assert result["output_local_file"] == "local_path.xlsx"
        assert result["output_s3_file"] == "s3_path.xlsx"
        mock_search_car.assert_called_once()
        mock_get_geom.assert_called_once()
        mock_apply_soil_use.assert_called_once()

    @patch("pb_agro_report_rgca_api.services.diagnosis_service.ExcelService")
    @patch("pb_agro_report_rgca_api.services.diagnosis_service.FileService")
    @patch("pb_agro_report_rgca_api.services.diagnosis_service.UtilsService")
    @patch("pb_agro_report_rgca_api.services.diagnosis_service.AgrowatchService")
    def test_generate_report_removes_columns_and_calls_excel(
        self,
        mock_AgrowatchService,
        mock_UtilsService,
        mock_FileService,
        mock_ExcelService,
    ):

        mock_utils = mock_UtilsService.return_value
        mock_excel = mock_ExcelService.return_value
        mock_file = mock_FileService.return_value

        # DataFrame with all columns to be removed
        input_df = pd.DataFrame(
            {
                "id": [1],
                "requester": ["r"],
                "reference_date": ["2020-01-01"],
                "updated_at": ["2020-01-01"],
                "errorType": [""],
                "trace": [""],
                "errorMessage": [""],
                "documento": ["doc1"],
                "car": ["car1"],
                "geom": ["geom"],
            }
        )
        mock_utils.read_input_csv.return_value = input_df.copy()
        mock_utils.remove_columns.side_effect = lambda df, cols: df.drop(
            columns=[c for c in cols if c in df.columns], errors="ignore"
        )
        mock_utils.reorder_dataframe.side_effect = lambda df: df
        mock_excel.save_file_to_excel.return_value = (
            "local_path.xlsx",
            "file_name.xlsx",
        )
        mock_file.save_file_to_s3.return_value = "s3_path.xlsx"

        # Patch _apply_soil_use to just return the df
        with patch.object(
            DiagnosisService, "_apply_soil_use", return_value=input_df.copy()
        ):
            service = DiagnosisService()
            result = service.generate_report(
                "fake_path.csv", [SubModules.AGRICULTURAL.value], "client_doc"
            )

        assert result["output_local_file"] == "local_path.xlsx"
        assert result["output_s3_file"] == "s3_path.xlsx"
        mock_utils.remove_columns.assert_called()
        mock_excel.save_file_to_excel.assert_called_once()
        mock_file.save_file_to_s3.assert_called_once()

    @patch("pb_agro_report_rgca_api.services.diagnosis_service.ESGService")
    def test_apply_esg_calls_process_esg_intersections(self, mock_ESGService):
        # Arrange
        mock_esg_service = mock_ESGService.return_value
        input_df = pd.DataFrame(
            {"car": ["car1", "car2"], "documento": ["11111111111", "22222222222"]}
        )
        processed_df = pd.DataFrame(
            {
                "car": ["car1", "car2"],
                "esg": [1, 2],
                "documento": ["11111111111", "22222222222"],
            }
        )
        mock_esg_service.process_esg_intersections.return_value = processed_df

        service = DiagnosisService()

        # Act
        result_df = service._apply_esg(input_df)

        # Assert
        mock_esg_service.process_esg_intersections.assert_called_once_with(input_df)
        pd.testing.assert_frame_equal(result_df, processed_df)

    @patch("pb_agro_report_rgca_api.services.diagnosis_service.ESGService")
    def test_apply_esg_returns_input_df_if_no_changes(self, mock_ESGService):
        # Arrange
        mock_esg_service = mock_ESGService.return_value
        input_df = pd.DataFrame({"car": ["car1"]})
        mock_esg_service.process_esg_intersections.return_value = input_df

        service = DiagnosisService()

        # Act
        result_df = service._apply_esg(input_df)

        # Assert
        pd.testing.assert_frame_equal(result_df, input_df)

    @patch("pb_agro_report_rgca_api.services.diagnosis_service.gpd.read_postgis")
    def test_get_car_geometries(self, mock_read_postgis):
        valid_car_a = f"AM-{'0' * 7}-{'A' * 32}"
        valid_car_b = f"AM-{'0' * 7}-{'B' * 32}"
        valid_car_c = f"AM-{'0' * 7}-{'C' * 32}"
        list_valid_cars = [valid_car_a, valid_car_b, valid_car_c]

        input_df = pd.DataFrame({"car": list_valid_cars})
        service = DiagnosisService()

        mock_read_postgis.return_value = pd.DataFrame(
            {"federal_car_code": list_valid_cars, "geom": ["geom1", "geom2", "geom3"]}
        )

        result_df = service.get_car_geometries(input_df)

        expected_df = pd.DataFrame(
            {
                "car": list_valid_cars,
                "car_invalid": [False, False, False],
                "geom": ["geom1", "geom2", "geom3"],
                "car_not_found": [False, False, False],
            }
        )

        pd.testing.assert_frame_equal(result_df, expected_df)
        mock_read_postgis.assert_called_once()

    @patch("pb_agro_report_rgca_api.services.diagnosis_service.gpd.read_postgis")
    def test_get_car_geometries_with_not_evaluated(self, mock_read_postgis):
        valid_car_a = f"AM-{'0' * 7}-{'A' * 32}"
        invalid_car_b = f"AM-{'0' * 7}-{'B' * 31}"
        not_found_car_c = f"AM-{'0' * 7}-{'C' * 32}"
        list_valid_cars = [valid_car_a, invalid_car_b, not_found_car_c]

        input_df = pd.DataFrame({"car": list_valid_cars})
        service = DiagnosisService()

        mock_read_postgis.return_value = pd.DataFrame(
            {"federal_car_code": [valid_car_a], "geom": ["geom1"]}
        )

        result_df = service.get_car_geometries(input_df)

        expected_df = pd.DataFrame(
            {
                "car": list_valid_cars,
                "car_invalid": [False, True, False],
                "geom": ["geom1", None, None],
                "car_not_found": [False, False, True],
            }
        )

        pd.testing.assert_frame_equal(result_df, expected_df)
        mock_read_postgis.assert_called_once()


if __name__ == "__main__":
    unittest.main()
