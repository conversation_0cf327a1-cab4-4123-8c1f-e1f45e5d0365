from unittest.mock import MagicMock, patch

import pytest
from fastapi import Request
from requests.exceptions import HTTPError

from pb_agro_report_rgca_api.api_auth import get_iam_token


@pytest.fixture
def mock_request():
    request = MagicMock(spec=Request)
    request.headers = {}
    return request


@patch("pb_agro_report_rgca_api.api_auth.IamTokenValidationService")
@patch("os.getenv")
def test_get_iam_token_success(mock_getenv, mock_iam_service, mock_request):
    # Mock environment variable
    mock_getenv.return_value = "http://mock-api-users-url"

    # Mock request headers
    mock_request.headers = {"Authorization": "Bearer mock_token"}

    # Mock IAM service behavior
    mock_iam_instance = MagicMock()
    mock_iam_instance.handle.return_value = {"user": "mock_user"}
    mock_iam_service.return_value = mock_iam_instance

    # Call the function
    result = get_iam_token(mock_request)

    # Assertions
    assert result == {"user": "mock_user"}
    mock_getenv.assert_called_once_with("API_USERS_BASE_URL")
    mock_iam_service.assert_called_once_with("http://mock-api-users-url")
    mock_iam_instance.handle.assert_called_once_with("mock_token", "AGRO_REPORT")


@patch("pb_agro_report_rgca_api.api_auth.IamTokenValidationService")
@patch("os.getenv")
def test_get_iam_token_missing_authorization_header(
    mock_getenv, mock_iam_service, mock_request
):
    # Call the function and assert it raises HTTPError
    with pytest.raises(HTTPError) as exc_info:
        get_iam_token(mock_request)

    assert "401" in str(exc_info.value)


@patch("pb_agro_report_rgca_api.api_auth.IamTokenValidationService")
@patch("os.getenv")
def test_get_iam_token_invalid_authorization_header(
    mock_getenv, mock_iam_service, mock_request
):
    # Mock request headers
    mock_request.headers = {"Authorization": "InvalidToken"}

    # Call the function and assert it raises HTTPError
    with pytest.raises(HTTPError) as exc_info:
        get_iam_token(mock_request)

    assert "401" in str(exc_info.value)


@patch("pb_agro_report_rgca_api.api_auth.IamTokenValidationService")
@patch("os.getenv")
def test_get_iam_token_iam_service_404(mock_getenv, mock_iam_service, mock_request):
    # Mock environment variable
    mock_getenv.return_value = "http://mock-api-users-url"

    # Mock request headers
    mock_request.headers = {"Authorization": "Bearer mock_token"}

    # Mock IAM service behavior
    mock_iam_instance = MagicMock()
    mock_iam_instance.handle.side_effect = HTTPError(
        response=MagicMock(status_code=404)
    )
    mock_iam_service.return_value = mock_iam_instance

    # Call the function and assert it raises HTTPError
    with pytest.raises(HTTPError) as exc_info:
        get_iam_token(mock_request)

    assert "403" in str(exc_info.value)


@patch("pb_agro_report_rgca_api.api_auth.IamTokenValidationService")
@patch("os.getenv")
def test_get_iam_token_iam_service_error(mock_getenv, mock_iam_service, mock_request):
    # Mock environment variable
    mock_getenv.return_value = "http://mock-api-users-url"

    # Mock request headers
    mock_request.headers = {"Authorization": "Bearer mock_token"}

    # Mock IAM service behavior
    mock_iam_instance = MagicMock()
    mock_iam_instance.handle.side_effect = Exception("Unexpected error")
    mock_iam_service.return_value = mock_iam_instance

    # Call the function and assert it raises HTTPError
    with pytest.raises(HTTPError) as exc_info:
        get_iam_token(mock_request)

    assert "401" in str(exc_info.value)
