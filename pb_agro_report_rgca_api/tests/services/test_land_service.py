from collections import namedtuple
from unittest.mock import MagicMock, patch

import pandas as pd
import pytest

from pb_agro_report_rgca_api.services.land_service import LandService


@pytest.fixture
def sample_input_df():
    return pd.DataFrame({"car": ["AC123", "MT456", "TO789", "PA321", "12345"]})


@pytest.fixture
def land_service():
    with patch(
        "pb_agro_report_rgca_api.services.land_service.UtilsService"
    ) as MockUtils, patch(
        "pb_agro_report_rgca_api.services.land_service.Main"
    ) as MockMain:
        mock_utils = MockUtils.return_value
        mock_utils.get_engine.return_value = MagicMock()
        mock_utils.rename_columns.side_effect = lambda df, mapping: df.rename(
            columns=mapping
        )
        mock_utils.remask_document.side_effect = lambda doc: f"masked_{doc}"
        mock_main = MockMain.return_value
        mock_main.decrypt_aes_ctr.side_effect = lambda d: f"decrypted_{d['data']}"
        yield LandService()


@patch.object(LandService, "_execute_stmt")
def test_get_owners_dataframe_calls_execute_stmt(
    mock_execute_stmt, land_service, sample_input_df
):
    # Mock _execute_stmt to return fake db results
    mock_execute_stmt.side_effect = [
        [("12345", ["docA"], ["nameA"])],  # mgi_car_property_relations_m
        [("AC123", ["docB"], ["nameB"])],  # sema_imac_ac_car_a
        [("MT456", ["docC"], ["nameC"])],  # sema_mt_car_a
        [("TO789", ["docD"], ["nameD"])],  # semarh_to_car_a
        [("PA321", ["docE"], ["nameE"])],  # semas_pa_car_a
    ]
    result_df = land_service.get_owners_dataframe(sample_input_df)
    # Check columns renamed
    assert "Proprietário1" in result_df.columns
    assert "Documento1" in result_df.columns
    # Check merge worked
    assert set(result_df["car"]) == set(sample_input_df["car"])
    # Check decrypted and remasked values
    for col in ["Documento1", "Documento2", "Documento3"]:
        assert (
            result_df[col].str.startswith("masked_decrypted_").all()
            or (result_df[col] == "-").all()
        )


def test_mount_dataframe_and_combine_data(land_service):
    combined_data = {
        "CAR1": {"docs": ["doc1", "doc2"], "names": ["name1", "name2"]},
        "CAR2": {"docs": ["doc3"], "names": ["name3"]},
    }
    df = land_service._mount_dataframe(combined_data)
    assert list(df.columns) == [
        "car",
        "nome1",
        "nome2",
        "nome3",
        "doc1",
        "doc2",
        "doc3",
    ]
    assert df.loc[df["car"] == "CAR1", "nome1"].iloc[0] == "name1"
    assert df.loc[df["car"] == "CAR2", "doc2"].iloc[0] == "-"


def test_decrypt_documents(land_service):
    df = pd.DataFrame({"doc1": ["a", "-"], "doc2": ["b", "-"], "doc3": ["-", "-"]})
    result = land_service._decrypt_documents(df.copy())
    assert result.loc[0, "doc1"].startswith("masked_decrypted_")
    assert result.loc[1, "doc1"] == "-"
    assert result.loc[0, "doc2"].startswith("masked_decrypted_")
    assert result.loc[1, "doc2"] == "-"
    assert result.loc[0, "doc3"] == "-"


@patch.object(LandService, "_execute_stmt")
def test_get_data_from_database_filters_by_state(
    mock_execute_stmt, land_service, sample_input_df
):
    # Only return something for AC and MT
    mock_execute_stmt.side_effect = [
        [],  # mgi_car_property_relations_m
        [("AC123", ["docB"], ["nameB"])],  # sema_imac_ac_car_a
        [("MT456", ["docC"], ["nameC"])],  # sema_mt_car_a
        [],  # semarh_to_car_a
        [],  # semas_pa_car_a
    ]
    results = land_service._get_data_from_database(sample_input_df)
    assert any("AC123" in r[0] for r in results if r)
    assert any("MT456" in r[0] for r in results if r)


def test_process_rectified_car_calls_rectified_service(land_service):
    # Mock the rectified_service.process_retified_car method
    mock_process = MagicMock()
    land_service.rectified_service.process_retified_car = mock_process

    input_df = pd.DataFrame({"car": ["X1", "X2"]})
    expected_df = pd.DataFrame({"car": ["Y1", "Y2"]})
    mock_process.return_value = expected_df

    result = land_service.process_rectified_car(input_df)

    mock_process.assert_called_once_with(input_df)
    pd.testing.assert_frame_equal(result, expected_df)


def test_process_rectified_car_returns_input_if_no_modification(land_service):
    # If process_retified_car returns the same DataFrame
    input_df = pd.DataFrame({"car": ["A", "B"]})
    land_service.rectified_service.process_retified_car = MagicMock(
        return_value=input_df
    )
    result = land_service.process_rectified_car(input_df)
    pd.testing.assert_frame_equal(result, input_df)


@patch.object(LandService, "_execute_stmt")
def test_get_car_metadata(mock_execute_stmt, sample_input_df):
    Row = namedtuple(
        "Row", ["federal_car_code", "ibge_code", "state", "area", "status"]
    )
    samples = [
        Row(
            federal_car_code="AC123",
            ibge_code="1507300",
            state="PI",
            area=2419.310302734375,
            status="AT",
        ),
        Row(
            federal_car_code="MT456",
            ibge_code="1507301",
            state="CE",
            area=2.343,
            status="PE",
        ),
        Row(
            federal_car_code="TO789",
            ibge_code="1507302",
            state="SP",
            area=0.31,
            status="AT",
        ),
        Row(
            federal_car_code="PA321",
            ibge_code="1507303",
            state="MG",
            area=42.09,
            status="AT",
        ),
        Row(
            federal_car_code="12345",
            ibge_code="1507304",
            state="TO",
            area=2244.31,
            status="AT",
        ),
    ]
    mock_execute_stmt.side_effect = [samples]

    result_df = LandService().get_car_metadata(sample_input_df)
    expected_columns = [
        "car",
        "Código Município",
        "Estado",
        "Área total do CAR (ha)",
        "Status CAR",
        "Região",
    ]
    assert result_df.shape == (len(samples), len(expected_columns))
    assert all(col in result_df.columns for col in expected_columns)


def test_anonymize_name_applies_to_all_name_columns(land_service):
    # Prepare a DataFrame with known values
    df = pd.DataFrame(
        {
            "nome1": ["Alice", "Bob"],
            "nome2": ["Carol", "Dave"],
            "nome3": ["Eve", "Frank"],
            "other": [1, 2],
        }
    )

    # Patch the utils.anonymize_name method to return a predictable value
    original_anonymize = land_service.utils.anonymize_name
    land_service.utils.anonymize_name = MagicMock(side_effect=lambda x: f"anon_{x}")

    result_df = land_service._anonymize_name(df.copy())

    actual_calls = [
        call.args for call in land_service.utils.anonymize_name.call_args_list
    ]
    assert sorted(actual_calls) == sorted(
        [("Alice",), ("Bob",), ("Carol",), ("Dave",), ("Eve",), ("Frank",)]
    )

    # Check that the columns were anonymized
    for col in ["nome1", "nome2", "nome3"]:
        assert all(val.startswith("anon_") for val in result_df[col])

    # Check that other columns are unchanged
    assert (result_df["other"] == df["other"]).all()

    # Restore original method
    land_service.utils.anonymize_name = original_anonymize


def test_anonymize_name_handles_missing_columns(land_service):
    # DataFrame missing nome2 and nome3
    df = pd.DataFrame(
        {
            "nome1": ["Alice", "Bob"],
            "other": [1, 2],
        }
    )

    # Patch anonymize_name
    land_service.utils.anonymize_name = MagicMock(side_effect=lambda x: f"anon_{x}")

    # Should raise KeyError if column is missing
    with pytest.raises(KeyError):
        land_service._anonymize_name(df)
