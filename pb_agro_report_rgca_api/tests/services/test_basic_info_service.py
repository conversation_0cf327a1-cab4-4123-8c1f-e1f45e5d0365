import unittest
from unittest.mock import patch

from pb_agro_report_rgca_api.services.basic_info_service import BasicInfoService


class TestBasicInfoService(unittest.TestCase):
    @patch("pb_agro_report_rgca_api.services.basic_info_service.CloudRangerService")
    def setUp(self, mock_cloud_ranger_service):
        self.mock_cloudranger = mock_cloud_ranger_service.return_value
        self.service = BasicInfoService()

    def test_get_basic_info_returns_correct_data(self):
        self.mock_cloudranger.get_data.return_value = {
            "records": [
                {
                    "basic_data": {
                        "tax_id_status": "REGULAR",
                        "birth_date": "2000-01-01",
                    },
                    "addresses": [{"uf": "RJ"}],
                },
            ]
        }
        result = self.service.get_basic_info({"documento": "12345678910"})
        expected_result = ("SIM", "SIM", {"RJ"})
        self.assertEqual(result, expected_result)

    def test_get_basic_info_handles_empty_records(self):
        self.mock_cloudranger.get_data.return_value = {"records": []}
        result = self.service.get_basic_info({"documento": "12345678910"})
        self.assertEqual(
            result,
            ("Consulta indisponível", "Consulta indisponível", "Consulta indisponível"),
        )

    def test_get_valid_cpf_sim(self):
        response = {
            "basic_data": {
                "tax_id_status": "REGULAR",
                "birth_date": "2000-01-01",
            }
        }
        result = self.service.get_valid_cpf(response)
        self.assertEqual(result, "SIM")

    def test_get_valid_cpf_nao(self):
        response = [
            {"basic_data": {"tax_id_status": "IRREGULAR"}},
            {"basic_data": {"tax_id_status": "INATIVA"}},
            {"basic_data": {"tax_id_status": ""}},
            {"basic_data": {"tax_id_status": None}},
        ]

        for record in response:
            result = self.service.get_valid_cpf(record)
            self.assertEqual(result, "NÃO")

    def test_get_valid_cpf_exception(self):
        self.mock_cloudranger.get_valid_cpf.side_effect = Exception("Some error")
        result = self.service.get_valid_cpf({})
        self.assertEqual(result, "Consulta indisponível")

    def test_get_age_above_21_sim(self):
        response = {"basic_data": {"birth_date": "2000-01-01"}}
        result = self.service.get_age_above_21(response)
        self.assertEqual(result, "SIM")

    def test_get_age_above_21_nao(self):
        response = {"basic_data": {"birth_date": "2010-01-01"}}
        result = self.service.get_age_above_21(response)
        self.assertEqual(result, "NÃO")

    def test_get_age_above_21_exception(self):
        self.mock_cloudranger.get_age_above_21.side_effect = Exception("Some error")
        result = self.service.get_age_above_21({})
        self.assertEqual(result, "Consulta indisponível")


if __name__ == "__main__":
    unittest.main()
