import json
import os
import unittest
from datetime import datetime
from unittest.mock import MagicMock, patch

from pb_agro_report_rgca_api.services.get_iam_token import (
    TOKEN_IN_MEMORY,
    GetIAMCredentials,
)


class TestGetIAMCredentials(unittest.TestCase):
    @patch.dict(
        os.environ,
        {
            "URL_DIGITAL_PAAS": "https://example.com",
            "IAM_CLIENT_ID": "test_client_id",
            "IAM_CLIENT_SECRET": "test_client_secret",
        },
    )
    def setUp(self):
        self.iam_credentials = GetIAMCredentials()
        TOKEN_IN_MEMORY[self.iam_credentials.username] = {
            "test_client_id": {"token": None, "expires_at": None}
        }

    @patch("pb_agro_report_rgca_api.services.get_iam_token.requests.post")
    @patch("pb_agro_report_rgca_api.services.get_iam_token.datetime")
    def test_get_token_new_token(self, mock_datetime, mock_post):
        mock_datetime.now.return_value = datetime(2023, 1, 1, 0, 0, 0)
        mock_response = MagicMock()
        mock_response.content = json.dumps({"accessToken": "new_token"}).encode("utf-8")
        mock_post.return_value = mock_response

        token = self.iam_credentials.get_token()

        self.assertEqual(token, "new_token")
        self.assertEqual(
            TOKEN_IN_MEMORY[self.iam_credentials.username]["token"], "new_token"
        )
        expires_at = datetime(2023, 1, 1, 0, 0, 0).timestamp() + 1800
        self.assertEqual(
            TOKEN_IN_MEMORY[self.iam_credentials.username]["expires_at"], expires_at
        )

    @patch("pb_agro_report_rgca_api.services.get_iam_token.datetime")
    def test_get_token_cached_token(self, mock_datetime):
        TOKEN_IN_MEMORY[self.iam_credentials.username]["token"] = "cached_token"
        TOKEN_IN_MEMORY[self.iam_credentials.username]["expires_at"] = (
            datetime.now().timestamp() + 1800
        )
        mock_datetime.now.return_value = datetime.now()

        token = self.iam_credentials.get_token()

        self.assertEqual(token, "cached_token")

    @patch("pb_agro_report_rgca_api.services.get_iam_token.requests.post")
    @patch("pb_agro_report_rgca_api.services.get_iam_token.datetime")
    def test_get_token_expired_token(self, mock_datetime, mock_post):
        TOKEN_IN_MEMORY[self.iam_credentials.username]["token"] = "expired_token"
        TOKEN_IN_MEMORY[self.iam_credentials.username]["expires_at"] = 1672531200
        mock_datetime.now.return_value = datetime(2023, 1, 1, 0, 30, 1)
        mock_response = MagicMock()
        mock_response.content = json.dumps({"accessToken": "new_token"}).encode("utf-8")
        mock_post.return_value = mock_response

        token = self.iam_credentials.get_token()

        self.assertEqual(token, "new_token")
        self.assertEqual(
            TOKEN_IN_MEMORY[self.iam_credentials.username]["token"], "new_token"
        )
        expires_at = datetime(2023, 1, 1, 0, 30, 1).timestamp() + 1800
        self.assertEqual(
            int(TOKEN_IN_MEMORY[self.iam_credentials.username]["expires_at"]),
            expires_at,
        )


if __name__ == "__main__":
    unittest.main()
