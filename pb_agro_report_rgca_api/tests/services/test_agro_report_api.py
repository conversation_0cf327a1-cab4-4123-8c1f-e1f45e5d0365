import unittest
from unittest.mock import MagicMock, patch

from pb_agro_report_rgca_api.services.agro_report_api import AgroReportApiService


class TestAgroReportApiService(unittest.TestCase):
    def setUp(self):
        self.user_id = "test_user"
        self.service = AgroReportApiService(self.user_id)
        self.path = "test_path"
        self.params = {"param1": "value1"}
        self.data = {"data1": "value1"}
        self.json = {"json1": "value1"}
        self.files = {"file1": "value1"}

    @patch("pb_agro_report_rgca_api.services.agro_report_api.GetIAMCredentials")
    def test_get_headers(self, mock_get_iam_credentials):
        mock_token = "test_token"
        mock_get_iam_credentials.return_value.get_token.return_value = mock_token

        headers = self.service.get_headers()

        self.assertEqual(headers["Authorization"], f"Bearer {mock_token}")
        self.assertEqual(headers["User-Id"], self.user_id)

    @patch("pb_agro_report_rgca_api.services.agro_report_api.requests.get")
    @patch(
        "pb_agro_report_rgca_api.services.agro_report_api.AgroReportApiService."
        "get_headers"
    )
    def test_get(self, mock_get_headers, mock_requests_get):
        mock_response = MagicMock()
        mock_response.raise_for_status = MagicMock()
        mock_requests_get.return_value = mock_response
        mock_get_headers.return_value = {"Authorization": "Bearer test_token"}

        response = self.service.get(self.path, self.params)

        mock_requests_get.assert_called_once_with(
            url=self.service.url + self.path,
            params=self.params,
            headers=mock_get_headers.return_value,
            timeout=30,
        )
        mock_response.raise_for_status.assert_called_once()
        self.assertEqual(response, mock_response)

    @patch("pb_agro_report_rgca_api.services.agro_report_api.requests.post")
    @patch(
        "pb_agro_report_rgca_api.services.agro_report_api.AgroReportApiService."
        "get_headers"
    )
    def test_post(self, mock_get_headers, mock_requests_post):
        mock_response = MagicMock()
        mock_response.raise_for_status = MagicMock()
        mock_requests_post.return_value = mock_response
        mock_get_headers.return_value = {"Authorization": "Bearer test_token"}

        response = self.service.post(
            self.path, self.params, self.data, self.json, files=self.files
        )

        mock_requests_post.assert_called_once_with(
            url=self.service.url + self.path,
            params=self.params,
            data=self.data,
            json=self.json,
            headers=mock_get_headers.return_value,
            files=self.files,
            timeout=30,
        )
        mock_response.raise_for_status.assert_called_once()
        self.assertEqual(response, mock_response)

    @patch("pb_agro_report_rgca_api.services.agro_report_api.requests.patch")
    @patch(
        "pb_agro_report_rgca_api.services.agro_report_api.AgroReportApiService."
        "get_headers"
    )
    def test_patch(self, mock_get_headers, mock_requests_patch):
        mock_response = MagicMock()
        mock_response.raise_for_status = MagicMock()
        mock_requests_patch.return_value = mock_response
        mock_get_headers.return_value = {"Authorization": "Bearer test_token"}

        response = self.service.patch(self.path, self.data, self.json)

        mock_requests_patch.assert_called_once_with(
            url=self.service.url + self.path,
            data=self.data,
            json=self.json,
            headers=mock_get_headers.return_value,
            timeout=30,
        )
        mock_response.raise_for_status.assert_called_once()
        self.assertEqual(response, mock_response)

    @patch("pb_agro_report_rgca_api.services.agro_report_api.requests.delete")
    @patch(
        "pb_agro_report_rgca_api.services.agro_report_api.AgroReportApiService."
        "get_headers"
    )
    def test_delete(self, mock_get_headers, mock_requests_delete):
        mock_response = MagicMock()
        mock_response.raise_for_status = MagicMock()
        mock_requests_delete.return_value = mock_response
        mock_get_headers.return_value = {"Authorization": "Bearer test_token"}

        response = self.service.delete(self.path)

        mock_requests_delete.assert_called_once_with(
            url=self.service.url + self.path,
            headers=mock_get_headers.return_value,
            timeout=30,
        )
        mock_response.raise_for_status.assert_called_once()
        self.assertEqual(response, mock_response)


if __name__ == "__main__":
    unittest.main()
