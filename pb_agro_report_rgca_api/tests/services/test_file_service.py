import os
import unittest
from tempfile import tempdir
from unittest.mock import MagicMock, patch

from pb_agro_report_rgca_api.services.file_service import FileService


class TestFileService(unittest.TestCase):

    @patch("pb_agro_report_rgca_api.services.file_service.os.getenv")
    def setUp(self, mock_getenv):
        mock_getenv.return_value = "test-bucket"
        self.file_service = FileService()

    @patch("pb_agro_report_rgca_api.services.file_service.os.path.exists")
    @patch("pb_agro_report_rgca_api.services.file_service.os.remove")
    def test_remove_local_file(self, mock_remove, mock_exists):
        mock_exists.return_value = True
        self.file_service.remove_local_file("test_file")
        mock_remove.assert_called_once_with("test_file")

    def test_make_temp_folder(self):
        temp_folder = self.file_service.make_temp_folder()
        self.assertTrue(os.path.exists(temp_folder.name))
        temp_folder.cleanup()

    @patch("pb_agro_report_rgca_api.services.file_service.requests.get")
    @patch("pb_agro_report_rgca_api.services.file_service.tempfile.gettempdir")
    def test_download_file(self, mock_gettempdir, mock_requests_get):
        mock_gettempdir.return_value = tempdir
        mock_response = MagicMock()
        mock_response.content = b"test content"
        mock_requests_get.return_value = mock_response

        file_path = self.file_service.download_file(
            "http://example.com/test", "test_file"
        )
        self.assertEqual(file_path, f"{tempdir}/test_file")
        self.assertTrue(os.path.isfile(file_path))
        os.remove(file_path)

    @patch("pb_agro_report_rgca_api.services.file_service.boto3.client")
    def test_save_file_to_s3(self, mock_boto3_client):
        mock_s3_client = MagicMock()
        mock_boto3_client.return_value = mock_s3_client

        output_s3_file = self.file_service.save_file_to_s3(
            "client_doc", "test_file", "local_file"
        )
        self.assertEqual(output_s3_file, "client_doc/test_file")
        mock_s3_client.upload_file.assert_called_once_with(
            "local_file", "test-bucket", "client_doc/test_file"
        )


if __name__ == "__main__":
    unittest.main()
