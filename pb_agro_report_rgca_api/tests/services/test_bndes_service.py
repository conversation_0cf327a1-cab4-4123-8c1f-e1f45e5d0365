import unittest
from unittest.mock import patch

from pb_agro_report_rgca_api.services.bndes_service import BNDESAnalysisService


class TestBNDESAnalysisService(unittest.TestCase):
    @patch("pb_agro_report_rgca_api.services.bndes_service.CloudRangerService")
    def setUp(self, mock_cloud_ranger_service):
        self.mock_cloudranger = mock_cloud_ranger_service.return_value
        self.service = BNDESAnalysisService()

    def test_get_bndes_sim(self):
        self.mock_cloudranger.get_data.return_value = {"records": [1, 2, 3]}
        result = self.service.get_bndes("some_document")
        self.assertEqual(result, "SIM")

    def test_get_bndes_nao(self):
        self.mock_cloudranger.get_data.return_value = {"records": []}
        result = self.service.get_bndes("some_document")
        self.assertEqual(result, "NÃO")

    def test_get_bndes_exception(self):
        self.mock_cloudranger.get_data.side_effect = Exception("Some error")
        result = self.service.get_bndes("some_document")
        self.assertEqual(result, "Consulta Indisponível")


if __name__ == "__main__":
    unittest.main()
