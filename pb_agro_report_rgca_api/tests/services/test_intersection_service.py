from unittest.mock import MagicMock, patch

import pytest
from shapely.geometry import Point

from pb_agro_report_rgca_api.enums.dataset import Dataset
from pb_agro_report_rgca_api.services.intersection_service import (
    TABLES,
    IntersectorService,
)


@pytest.fixture
def mock_utils_service(monkeypatch):
    # Patch UtilsService.get_engine to return a mock engine
    mock_engine = MagicMock()
    monkeypatch.setattr(
        "pb_agro_report_rgca_api.services.utils_service.UtilsService.get_engine",
        lambda self, db_name=None: mock_engine,
    )
    return mock_engine


@pytest.fixture
def intersector_service(mock_utils_service):
    return IntersectorService()


@pytest.fixture
def mock_row():
    return {"geom": Point(1, 1)}


@patch("pb_agro_report_rgca_api.services.intersection_service.Table")
@patch("pb_agro_report_rgca_api.services.intersection_service.select")
def test_get_intersection_single_table_intersects(
    mock_select, mock_table, intersector_service, mock_row
):
    # Setup
    dataset = Dataset.ANA_PIVO
    # Patch _check_intersection to return True
    intersector_service._check_intersection = MagicMock(return_value=True)
    result = intersector_service.get_intersection(mock_row, dataset)
    assert result == "SIM"
    intersector_service._check_intersection.assert_called_once()


@patch("pb_agro_report_rgca_api.services.intersection_service.Table")
@patch("pb_agro_report_rgca_api.services.intersection_service.select")
def test_get_intersection_single_table_no_intersection(
    mock_select, mock_table, intersector_service, mock_row
):
    dataset = Dataset.ANA_PIVO
    intersector_service._check_intersection = MagicMock(return_value=False)
    result = intersector_service.get_intersection(mock_row, dataset)
    assert result == "NÃO"
    intersector_service._check_intersection.assert_called_once()


@patch("pb_agro_report_rgca_api.services.intersection_service.Table")
@patch("pb_agro_report_rgca_api.services.intersection_service.select")
def test_get_intersection_multiple_tables_first_intersects(
    mock_select, mock_table, intersector_service, mock_row
):
    dataset = Dataset.PRODES
    # First table returns True, so should return "SIM" immediately
    intersector_service._check_intersection = MagicMock(
        side_effect=[True, False, False, False, False, False]
    )
    result = intersector_service.get_intersection(mock_row, dataset)
    assert result == "SIM"
    intersector_service._check_intersection.assert_called_with(
        mock_row, TABLES[dataset][0]
    )


@patch("pb_agro_report_rgca_api.services.intersection_service.Table")
@patch("pb_agro_report_rgca_api.services.intersection_service.select")
def test_get_intersection_multiple_tables_none_intersect(
    mock_select, mock_table, intersector_service, mock_row
):
    dataset = Dataset.PRODES
    intersector_service._check_intersection = MagicMock(
        side_effect=[False] * len(TABLES[dataset])
    )
    result = intersector_service.get_intersection(mock_row, dataset)
    assert result == "NÃO"
    assert intersector_service._check_intersection.call_count == len(TABLES[dataset])


def test_get_intersection_invalid_dataset(intersector_service, mock_row):
    class FakeDataset:
        value = "INVALID"

    with pytest.raises(ValueError) as excinfo:
        intersector_service.get_intersection(mock_row, FakeDataset())
    assert "Tabela não permitida" in str(excinfo.value)


def test_check_intersection_true(intersector_service, mock_utils_service):
    # Mock do engine e conexão
    intersector_service._get_stmt = MagicMock()
    intersector_service._execute_stmt = MagicMock(return_value=True)
    # Instancia a classe e executa
    row = {"geom": Point(-43.1729, -22.9068)}
    result = intersector_service._check_intersection(row, "tabela_teste")
    assert result is True


def test_check_intersection_false(
    intersector_service,
    mock_row,
    mock_utils_service,
):
    intersector_service._get_stmt = MagicMock()
    intersector_service._execute_stmt = MagicMock(return_value=False)

    result = intersector_service._check_intersection(mock_row, "ana_pivos_m")
    assert result is False
