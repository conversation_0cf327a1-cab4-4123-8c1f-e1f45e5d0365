import os
import unittest
from unittest.mock import MagicMock, patch

from pb_agro_report_rgca_api.services.cloud_ranger_service import CloudRangerService


class TestCloudRangerService(unittest.TestCase):
    @patch.dict(
        os.environ,
        {
            "CLOUD_RANGER_BASE_URL": "https://example.com",
            "REQUESTS_HTTPS_VERIFY": "true",
        },
    )
    @patch("requests.post")
    def test_get_data(self, mock_post):
        mock_response = MagicMock()
        mock_response.content = '{"accessToken": "fake_token"}'
        mock_response.json.return_value = {"key": "value"}
        mock_response.raise_for_status = MagicMock()
        mock_post.return_value = mock_response

        service = CloudRangerService()
        url = "test_endpoint"
        payload = {"data": "test"}

        # Act
        result = service.get_data(url, payload)

        # Assert
        self.assertEqual(result, {"key": "value"})
        mock_post.assert_called_with(
            "https://example.com/test_endpoint",
            headers={
                "Authorization": "Bearer fake_token",
                "Content-Type": "application/json",
            },
            json=payload,
            timeout=60,
            verify=True,
        )
        mock_response.raise_for_status.assert_called_once()


if __name__ == "__main__":
    unittest.main()
