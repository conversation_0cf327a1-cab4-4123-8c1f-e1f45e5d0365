import unittest
from unittest.mock import patch

from pb_agro_report_rgca_api.services.sintegra_service import SintegraService


class TestBasicInfoService(unittest.TestCase):
    @patch("pb_agro_report_rgca_api.services.sintegra_service.CloudRangerService")
    def setUp(self, mock_cloud_ranger_service):
        self.mock_cloudranger = mock_cloud_ranger_service.return_value
        self.service = SintegraService()

    def test_get_sintegra_data_returns_sim_for_habilitado_status(self):
        self.mock_cloudranger.get_data.return_value = {
            "records": [{"status": "Ativo - HABILITADO"}]
        }
        result = self.service.get_sintegra_data(
            {"documento": "12345678910", "state": {"RJ"}}
        )
        self.assertEqual(result, "SIM")

    def test_get_sintegra_data_returns_nao_for_non_habilitado_status(self):
        self.mock_cloudranger.get_data.return_value = {
            "records": [{"status": "Inativo"}]
        }
        result = self.service.get_sintegra_data(
            {"documento": "12345678910", "state": {"RJ"}}
        )
        self.assertEqual(result, "NÃO")

    def test_get_sintegra_data_fallback_to_ie_status(self):
        self.mock_cloudranger.get_data.side_effect = [
            Exception("Some error"),
            {"records": [{"ie_status": "Habilitado"}]},
        ]
        result = self.service.get_sintegra_data(
            {"documento": "12345678910", "state": {"RJ"}}
        )
        self.assertEqual(result, "SIM")

    def test_get_sintegra_data_fallback_returns_nao_for_non_habilitado_ie_status(self):
        self.mock_cloudranger.get_data.side_effect = [
            Exception("Some error"),
            {"records": [{"ie_status": "Inativo"}]},
        ]
        result = self.service.get_sintegra_data(
            {"documento": "12345678910", "state": {"RJ"}}
        )
        self.assertEqual(result, "NÃO")

    def test_get_sintegra_data_handles_double_exception(self):
        self.mock_cloudranger.get_data.side_effect = Exception("Some error")
        result = self.service.get_sintegra_data(
            {"documento": "12345678910", "state": {"RJ"}}
        )
        self.assertEqual(result, "Consulta indisponível")

    def test_get_sintegra_from_ccc_sim(self):
        self.mock_cloudranger.get_data.return_value = {
            "records": [{"ie_status": "Habilitado"}]
        }
        payload = {"documento": "some_document", "state": "some_state"}
        result = self.service.get_sintegra_from_ccc(payload)
        self.assertEqual(result, "SIM")

    def test_get_sintegra_from_ccc_nao(self):
        self.mock_cloudranger.get_data.return_value = {
            "records": [{"ie_status": "Não Habilitado"}]
        }
        payload = {"documento": "some_document", "state": "some_state"}
        result = self.service.get_sintegra_from_ccc(payload)
        self.assertEqual(result, "NÃO")

    def test_get_sintegra_from_ccc_exception(self):
        self.mock_cloudranger.get_data.side_effect = Exception("Some error")
        payload = {"documento": "some_document", "state": "some_state"}
        result = self.service.get_sintegra_from_ccc(payload)
        self.assertEqual(result, "Consulta indisponível")


if __name__ == "__main__":
    unittest.main()
