import unittest
from unittest.mock import MagicMock, patch

import pandas as pd

from pb_agro_report_rgca_api.enums.dataset import Dataset
from pb_agro_report_rgca_api.services.soil_use_service import SoilUseService


class TestSoilUseService(unittest.TestCase):
    def setUp(self):
        self.expected_output = {
            "CAR1": {
                "Soja (2021/22)": "-",
                "<PERSON>ja (2022/23)": "SIM",
                "<PERSON><PERSON> (2023/24)": "-",
                "<PERSON><PERSON><PERSON> (2021/22)": "-",
                "<PERSON><PERSON><PERSON> (2022/23)": "NÃO",
                "<PERSON><PERSON><PERSON> (2023/24)": "-",
                "<PERSON><PERSON><PERSON> 2ª safra (2022/23)": "-",
                "Algod<PERSON> (2021/22)": "-",
                "Algodão (2022/23)": "-",
                "Algod<PERSON> (2023/24)": "-",
                "<PERSON><PERSON> (2021/22)": "-",
                "<PERSON><PERSON> (2022/23)": "-",
                "<PERSON><PERSON> (2023/24)": "-",
                "Café (2020/21)": "-",
                "Café (2021/22)": "-",
                "Café (2022/23)": "-",
                "Pasto (2020/21)": "-",
                "Pasto (2021/22)": "-",
                "Pasto (2022/23)": "-",
                "Vegetação nativa (2020/21)": "-",
                "Vegetação nativa (2021/22)": "-",
                "Vegetação nativa (2022/23)": "-",
                "Presença de Pivôs de Irrigação": "-",
                "Presença de Armazéns": "-",
            },
            "CAR2": {
                "Soja (2021/22)": "-",
                "Soja (2022/23)": "-",
                "Soja (2023/24)": "-",
                "Milho (2021/22)": "-",
                "Milho (2022/23)": "-",
                "Milho (2023/24)": "-",
                "Milho 2ª safra (2022/23)": "-",
                "Algodão (2021/22)": "NÃO",
                "Algodão (2022/23)": "-",
                "Algodão (2023/24)": "-",
                "Cana (2021/22)": "SIM",
                "Cana (2022/23)": "-",
                "Cana (2023/24)": "-",
                "Café (2020/21)": "-",
                "Café (2021/22)": "-",
                "Café (2022/23)": "-",
                "Pasto (2020/21)": "-",
                "Pasto (2021/22)": "-",
                "Pasto (2022/23)": "-",
                "Vegetação nativa (2020/21)": "-",
                "Vegetação nativa (2021/22)": "-",
                "Vegetação nativa (2022/23)": "-",
                "Presença de Pivôs de Irrigação": "-",
                "Presença de Armazéns": "-",
            },
        }

    @patch("os.getenv")
    def test_organize_results(self, mock_getenv):
        mock_getenv.side_effect = lambda key, v=None: (
            f"mock_{key.lower()}" if key != "MONGO_DB_PORT" else "5432"
        )
        results = [
            {"car": "CAR1", "year": 2023, "soybeans": 1, "corn": 0},
            {"car": "CAR2", "year": 2022, "sugar_cane": 1, "cotton": 0},
        ]

        service = SoilUseService()
        organized_results = service.organize_results(results, [])
        self.assertEqual(organized_results, self.expected_output)

    @patch("os.getenv")
    @patch("pb_agro_report_rgca_api.services.soil_use_service.pymongo.MongoClient")
    def test_get_soil_use(self, mock_mongo_client, mock_getenv):
        mock_getenv.side_effect = lambda key, v=None: (
            f"mock_{key.lower()}" if key != "MONGO_DB_PORT" else "5432"
        )
        mock_collection = MagicMock()
        mock_collection.find.return_value = [
            {"car": "CAR1", "year": 2023, "soybeans": 1, "corn": 0},
            {"car": "CAR2", "year": 2022, "sugar_cane": 1, "cotton": 0},
        ]

        mock_db = MagicMock()
        mock_db.soil_use_v3 = mock_collection

        mock_client_instance = MagicMock()
        mock_client_instance.__getitem__.return_value = mock_db
        mock_mongo_client.return_value = mock_client_instance

        cars = ["CAR1", "CAR2"]
        service = SoilUseService()
        soil_use = service.get_soil_use(cars)
        self.assertEqual(soil_use, self.expected_output)

    @patch("os.getenv")
    @patch("pb_agro_report_rgca_api.services.soil_use_service.IntersectorService")
    def test_check_landmarks_sets_intersection_and_handles_exceptions(
        self, mock_intersector_service, mock_getenv
    ):

        mock_getenv.side_effect = lambda key, v=None: (
            f"mock_{key.lower()}" if key != "MONGO_DB_PORT" else "5432"
        )

        # Prepare input DataFrame
        data = [
            {"car": "CAR1", "geom": "geom1"},
            {"car": "CAR2", "geom": "geom2"},
            {"car": "CAR3", "geom": None},  # Should be skipped
        ]
        input_df = pd.DataFrame(data)

        # Mock IntersectorService.get_intersection
        mock_instance = mock_intersector_service.return_value

        # First call returns True, second raises Exception, third should not be called (geom is None)
        def side_effect(row, dataset):
            if row["car"] == "CAR1":
                return True
            elif row["car"] == "CAR2":
                raise Exception("Test exception")

        mock_instance.get_intersection.side_effect = side_effect

        # Add columns for datasets to input_df
        input_df[Dataset.ANA_PIVO.value] = None
        input_df[Dataset.ARMAZENS.value] = None

        service = SoilUseService()
        result_df = service.check_landmarks(input_df.copy())

        # CAR1: Should be True for ANA_PIVO and ARMAZENS
        self.assertTrue(
            result_df.loc[result_df["car"] == "CAR1", Dataset.ANA_PIVO.value].values[0]
        )
        self.assertTrue(
            result_df.loc[result_df["car"] == "CAR1", Dataset.ARMAZENS.value].values[0]
        )

        # CAR2: Should be '-' for ANA_PIVO and ARMAZENS due to exception
        self.assertEqual(
            result_df.loc[result_df["car"] == "CAR2", Dataset.ANA_PIVO.value].values[0],
            "-",
        )
        self.assertEqual(
            result_df.loc[result_df["car"] == "CAR2", Dataset.ARMAZENS.value].values[0],
            "-",
        )

        # CAR3: Should remain None (geom is None, so not processed)
        self.assertIsNone(
            result_df.loc[result_df["car"] == "CAR3", Dataset.ANA_PIVO.value].values[0]
        )
        self.assertIsNone(
            result_df.loc[result_df["car"] == "CAR3", Dataset.ARMAZENS.value].values[0]
        )


if __name__ == "__main__":
    unittest.main()
