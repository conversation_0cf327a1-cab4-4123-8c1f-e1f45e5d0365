import os
import unittest
from unittest.mock import MagicMock, patch

from pb_agro_report_rgca_api.services.delivery_api_service import DeliveryApiService


class TestDeliveryApiService(unittest.TestCase):
    @patch.dict(
        os.environ,
        {
            "DELIVERY_API_URL": "https://example.com",
            "DELIVERY_API_EMAIL": "<EMAIL>",
            "DELIVERY_API_PASSWORD": "very_secret_passw0rd",
            "REQUESTS_HTTPS_VERIFY": "true",
        },
    )
    @patch("requests.post")
    def test_get_token(self, mock_post):
        mock_response = MagicMock()
        mock_response.content = '{"token": "fake_token"}'
        mock_response.json.return_value = {"key": "value"}
        mock_response.raise_for_status = MagicMock()
        mock_post.return_value = mock_response

        DeliveryApiService()

        payload = {
            "email": os.getenv("DELIVERY_API_EMAIL"),
            "password": os.getenv("DELIVERY_API_PASSWORD"),
        }
        mock_post.assert_called_with(
            "https://example.com/auth/login",
            json=payload,
            timeout=60,
            verify=True,
        )
        mock_response.raise_for_status.assert_called_once()

    @patch.dict(
        os.environ,
        {
            "DELIVERY_API_URL": "https://example.com",
            "DELIVERY_API_EMAIL": "<EMAIL>",
            "DELIVERY_API_PASSWORD": "very_secret_passw0rd",
            "REQUESTS_HTTPS_VERIFY": "true",
        },
    )
    @patch("requests.post")
    def test_create_consult(self, mock_post):
        mock_response = MagicMock()
        mock_response.json.return_value = {"token": "fake_token"}
        mock_response.raise_for_status = MagicMock()
        mock_post.return_value = mock_response

        service = DeliveryApiService()

        mock_response.json.return_value = {"_id": "123456789"}
        consult_id = service.create_consult(
            document="some_document",
            datasets=["dataset1", "dataset2"],
        )

        self.assertEqual(consult_id, "123456789")
        payload = {
            "document": "some_document",
            "datasets": ["dataset1", "dataset2"],
        }
        mock_post.assert_called_with(
            "https://example.com/consults",
            json=payload,
            headers={
                "Authorization": "Bearer fake_token",
            },
            timeout=60,
            verify=True,
        )
        mock_response.raise_for_status.assert_called()
        self.assertEqual(mock_response.raise_for_status.call_count, 1)

    @patch.dict(
        os.environ,
        {
            "DELIVERY_API_URL": "https://example.com",
            "DELIVERY_API_EMAIL": "<EMAIL>",
            "DELIVERY_API_PASSWORD": "very_secret_passw0rd",
            "REQUESTS_HTTPS_VERIFY": "true",
        },
    )
    @patch("requests.post")
    @patch("requests.get")
    def test_get_consult(self, mock_get, mock_post):
        mock_response = MagicMock()
        mock_response.json.return_value = {"token": "fake_token"}
        mock_response.raise_for_status = MagicMock()
        mock_post.return_value = mock_response

        service = DeliveryApiService()

        mock_response.json.return_value = {
            "datasets": [
                {
                    "type": "PROTESTS",
                    "status": "COMPLETED",
                    "results": [{"key": "value"}],
                }
            ]
        }
        mock_get.return_value = mock_response
        consult_data = service.get_consult("123456789")

        self.assertEqual(
            consult_data,
            [
                {
                    "results": [{"key": "value"}],
                    "status": "COMPLETED",
                    "type": "PROTESTS",
                }
            ],
        )

        mock_get.assert_called_with(
            "https://example.com/consults/123456789",
            headers={
                "Authorization": "Bearer fake_token",
            },
            timeout=60,
            verify=True,
        )
        mock_response.raise_for_status.assert_called()
        self.assertEqual(mock_response.raise_for_status.call_count, 1)

    @patch.dict(
        os.environ,
        {
            "DELIVERY_API_URL": "https://example.com",
            "DELIVERY_API_EMAIL": "<EMAIL>",
            "DELIVERY_API_PASSWORD": "very_secret_passw0rd",
            "REQUESTS_HTTPS_VERIFY": "true",
        },
    )
    @patch("requests.post")
    @patch("requests.get")
    def test_create_and_get_consult_returns_completed_response(
        self, mock_get, mock_post
    ):
        mock_response = MagicMock()
        mock_response.json.return_value = {"token": "fake_token"}
        mock_response.raise_for_status = MagicMock()
        mock_post.return_value = mock_response

        service = DeliveryApiService()

        mock_post.return_value.json.return_value = {"_id": "consult_id"}
        mock_get.return_value.json.return_value = {
            "datasets": [{"status": "COMPLETED", "results": ["dataset1", "dataset2"]}]
        }
        result = service.create_and_get_consult("123456789", ["dataset1", "dataset2"])
        self.assertEqual(
            result, {"status": "COMPLETED", "results": ["dataset1", "dataset2"]}
        )

    @patch.dict(
        os.environ,
        {
            "DELIVERY_API_URL": "https://example.com",
            "DELIVERY_API_EMAIL": "<EMAIL>",
            "DELIVERY_API_PASSWORD": "very_secret_passw0rd",
            "REQUESTS_HTTPS_VERIFY": "true",
        },
    )
    @patch("requests.post")
    @patch("requests.get")
    def test_create_and_get_consult_returns_consulta_indisponivel_for_failed_status(
        self, mock_get, mock_post
    ):
        mock_response = MagicMock()
        mock_response.json.return_value = {"token": "fake_token"}
        mock_response.raise_for_status = MagicMock()
        mock_post.return_value = mock_response

        service = DeliveryApiService()

        mock_post.return_value.json.return_value = {"_id": "consult_id"}
        mock_get.return_value.json.return_value = {
            "datasets": [{"status": "FAILED", "results": ["dataset1", "dataset2"]}]
        }
        result = service.create_and_get_consult("123456789", ["dataset1", "dataset2"])
        self.assertEqual(result, "Consulta indisponível")


if __name__ == "__main__":
    unittest.main()
