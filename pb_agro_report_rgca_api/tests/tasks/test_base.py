import unittest
from unittest.mock import MagicMock, patch

from requests import HTTPError

from pb_agro_report_rgca_api.enums.report_status import ReportStatus
from pb_agro_report_rgca_api.tasks.base import TaskBase


class TestTaskBase(unittest.TestCase):
    @patch("pb_agro_report_rgca_api.tasks.base.create_engine")
    @patch("os.getenv")
    def test_init(self, mock_getenv, mock_create_engine):
        mock_getenv.side_effect = lambda key, v=None: f"mock_{key.lower()}"
        task = TaskBase()
        mock_create_engine.assert_called_once_with(
            "postgresql://mock_db_user_app:mock_db_password_app@mock_db_host_app:mock_db_port_app/mock_db_database_app"  # noqa
        )
        self.assertIsNotNone(task.engine)

    @patch("os.getenv")
    @patch("pb_agro_report_rgca_api.tasks.base.sessionmaker")
    @patch.object(TaskBase, "set_new_status")
    def test_before_start(self, mock_set_new_status, mock_sessionmaker, mock_getenv):
        mock_getenv.side_effect = lambda key, v=None: (
            f"mock_{key.lower()}" if key != "DB_PORT_APP" else "5432"
        )
        mock_session = MagicMock()
        mock_sessionmaker.return_value = MagicMock(return_value=mock_session)
        task = TaskBase()
        task.before_start("task_id", [], {"key": "value"})
        self.assertIn("task_id", task.sessions)
        mock_set_new_status.assert_called_once_with(
            task_id="task_id",
            output_local_file="",
            new_status=ReportStatus.PROCESSING.value,
            key="value",
        )

    @patch("os.getenv")
    @patch.object(TaskBase, "set_new_status")
    def test_on_success(self, mock_set_new_status, mock_getenv):
        mock_getenv.side_effect = lambda key, v=None: (
            f"mock_{key.lower()}" if key != "DB_PORT_APP" else "5432"
        )
        task = TaskBase()
        retval = {"output_local_file": "test_file.xlsx"}
        task.on_success(retval, "task_id", [], {"key": "value"})
        mock_set_new_status.assert_called_once_with(
            task_id="task_id",
            output_local_file="test_file.xlsx",
            new_status=ReportStatus.DONE.value,
            key="value",
        )

    @patch("os.getenv")
    @patch.object(TaskBase, "set_new_status")
    @patch("pb_agro_report_rgca_api.tasks.base.build_failure_msg")
    @patch("pb_agro_report_rgca_api.tasks.base.TeamsMessagesService.send_teams_message")
    def test_on_failure(
        self,
        mock_send_teams_message,
        mock_build_failure_msg,
        mock_set_new_status,
        mock_getenv,
    ):
        mock_getenv.side_effect = lambda key, v=None: (
            f"mock_{key.lower()}" if key != "DB_PORT_APP" else "5432"
        )
        mock_build_failure_msg.return_value = ("Subject", "Message")
        task = TaskBase()
        exc = Exception("Test exception")
        task.on_failure(exc, "task_id", [], {"key": "value"}, None)
        mock_set_new_status.assert_called_once_with(
            task_id="task_id",
            new_status=ReportStatus.ERROR.value,
            output_local_file="",
            errors=True,
            key="value",
        )
        mock_send_teams_message.assert_called_once_with(
            subject="Subject", message="Message"
        )

    @patch("os.getenv")
    @patch("pb_agro_report_rgca_api.tasks.base.sessionmaker")
    def test_after_return(self, mock_sessionmaker, mock_getenv):
        mock_getenv.side_effect = lambda key, v=None: (
            f"mock_{key.lower()}" if key != "DB_PORT_APP" else "5432"
        )
        mock_session = MagicMock()
        mock_sessionmaker.return_value = MagicMock(return_value=mock_session)
        task = TaskBase()
        task.sessions["task_id"] = mock_session
        task.after_return(None, None, "task_id", [], {}, None)
        mock_session.close.assert_called_once()
        self.assertNotIn("task_id", task.sessions)

    @patch("os.getenv")
    @patch("pb_agro_report_rgca_api.tasks.base.AgroReportApiService")
    @patch("pb_agro_report_rgca_api.tasks.base.ReportRGCA")
    def test_set_new_status_with_exception(
        self, mock_report_rgca, mock_agro_report_api_service, mock_getenv
    ):
        mock_getenv.side_effect = lambda key, v=None: (
            f"mock_{key.lower()}" if key != "DB_PORT_APP" else "5432"
        )
        mock_session = MagicMock()
        mock_report_instance = MagicMock()
        mock_report_rgca.query.filter.return_value.first.return_value = (
            mock_report_instance
        )
        mock_agro_service_instance = MagicMock()
        mock_agro_service_instance.post.side_effect = Exception("Test exception")
        mock_agro_report_api_service.return_value = mock_agro_service_instance

        task = TaskBase()
        task.sessions["task_id"] = mock_session

        with self.assertRaises(HTTPError):
            task.set_new_status(
                task_id="task_id",
                user_id=1,
                report_id=2,
                rgca_id=3,
                output_local_file="test_file.xlsx",
                new_status=ReportStatus.DONE.value,
            )

        mock_session.rollback.assert_called_once()


if __name__ == "__main__":
    unittest.main()
