import os
from tempfile import tempdir
from unittest.mock import MagicMock, patch

import pytest

from pb_agro_report_rgca_api.tasks.diagnosis_report_task import diagnosis_report_task


@pytest.fixture
def mock_env():
    with patch.dict(os.environ, {"MEDIA_DIR": f"{tempdir}/media"}):
        yield


@patch("pb_agro_report_rgca_api.tasks.diagnosis_report_task.DiagnosisService")
def test_diagnosis_report_task_calls_generate_report(mock_diagnosis_service, mock_env):
    mock_service_instance = MagicMock()
    mock_diagnosis_service.return_value = mock_service_instance
    mock_service_instance.generate_report.return_value = {"result": "success"}

    document_filename = "report.pdf"
    sub_modules = ["mod1", "mod2"]
    client_document = "client123"

    result = diagnosis_report_task(
        document_filename, sub_modules, client_document=client_document
    )

    expected_path = os.path.join(f"{tempdir}/media", client_document, document_filename)
    mock_service_instance.generate_report.assert_called_once_with(
        expected_path, sub_modules, client_document
    )
    assert result == {"result": "success"}


@patch("pb_agro_report_rgca_api.tasks.diagnosis_report_task.DiagnosisService")
def test_diagnosis_report_task_missing_client_document(
    mock_diagnosis_service, mock_env
):
    mock_service_instance = MagicMock()
    mock_diagnosis_service.return_value = mock_service_instance
    mock_service_instance.generate_report.return_value = {"result": "no_client"}

    document_filename = "report.pdf"
    sub_modules = ["mod1", "mod2"]

    result = diagnosis_report_task(document_filename, sub_modules)

    expected_path = os.path.join(f"{tempdir}/media", "test", document_filename)
    mock_service_instance.generate_report.assert_called_once_with(
        expected_path, sub_modules, "test"
    )
    assert result == {"result": "no_client"}
