import os
import shutil
import tempfile
import traceback
from datetime import datetime
from typing import Optional

import boto3
from fastapi import APIRouter, Depends, File, HTTPException, Query, UploadFile
from fastapi.responses import FileResponse
from fastapi_sqlalchemy import db

from pb_agro_report_rgca_api.api_auth import get_iam_token
from pb_agro_report_rgca_api.enums.report_status import ReportStatus
from pb_agro_report_rgca_api.models import ReportRGCA
from pb_agro_report_rgca_api.schemas import ReportRGCACreateOut
from pb_agro_report_rgca_api.services.teams_messages import TeamsMessagesService

router = APIRouter(tags=["Report-RGCA"], dependencies=[Depends(get_iam_token)])

s3 = boto3.client("s3")


@router.post("/report-rgca", response_model=ReportRGCACreateOut)
def report_rgca_create(
    user_id: int,
    requester: str,
    client_document: str,
    report_id: int,
    sub_modules: Optional[str] = Query(default="[]"),
    document_file: UploadFile = File(...),
    webhook: Optional[str] = None,
) -> FileResponse:
    try:
        db_report_rgca = ReportRGCA(
            requester=requester,
            webhook=webhook,
            client_document=client_document,
            external_id=report_id,
        )
        db.session.add(db_report_rgca)
        db.session.commit()
        file_dir = os.path.join(os.getenv("MEDIA_DIR"), client_document)
        os.makedirs(file_dir, exist_ok=True)

        current_date = datetime.now().strftime("%Y%m%d%H%M%S")
        file_name = f"{current_date}_{document_file.filename}"
        with open(os.path.join(file_dir, file_name), "wb") as buffer:
            shutil.copyfileobj(document_file.file, buffer)

        from pb_agro_report_rgca_api import celery_app

        kwargs = {
            "document_filename": file_name,
            "rgca_id": db_report_rgca.id,
            "report_id": report_id,
            "user_id": user_id,
            "client_document": client_document,
        }
        if sub_modules:
            sub_modules_list = sub_modules.split(",")
            kwargs["sub_modules"] = sub_modules_list

        celery_app.send_task(
            "diagnosis_report_task",
            kwargs=kwargs,
        )

        return db_report_rgca
    except Exception as e:
        db.session.rollback()
        raise e


@router.get("/report-rgca/{id}")
def report_rgca_detail(id: int):
    report_rgca = db.session.query(ReportRGCA).filter(ReportRGCA.id == id).first()
    if report_rgca is None:
        raise HTTPException(status_code=404, detail="Report not found")

    response = {"report_progress_status": report_rgca.status}

    if report_rgca.status == ReportStatus.DONE.value:
        try:
            file_obj = s3.get_object(
                Bucket=os.getenv("RGCA_BUCKET"),
                Key=f"{report_rgca.client_document}/{report_rgca.filename}",
            )
            file_content = file_obj["Body"].read()
            with tempfile.NamedTemporaryFile(delete=False, suffix=".xlsx") as tmp:
                tmp.write(file_content)
                tmp_path = tmp.name

            return FileResponse(
                path=tmp_path,
                media_type="application/octet-stream",
                filename="diagnosis_report.xlsx",
            )
        except Exception as e:
            traceback.print_exc()
            TeamsMessagesService.send_teams_message(
                subject="Agro Score Detail Error",
                message=traceback.format_exception(e),
            )

    return response
