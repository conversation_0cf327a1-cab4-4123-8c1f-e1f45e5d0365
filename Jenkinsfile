pipeline {
  tools {
    terraform "1.2.7"
  }

  options {
    buildDiscarder(logRotator(numToKeepStr: "3"))
  }

  agent any

  environment {
    DOCKER_REGISTRY_PUT = "registry.agribusiness-brain.br.experian.eeca"
    DOCKER_REGISTRY = "dockerhub.agribusiness-brain.br.experian.eeca"
    DOCKER_IMAGE_AGENT_SONAR_SCANNER = "${DOCKER_REGISTRY}/sonarsource/sonar-scanner-cli:jenkins"
    DOCKER_IMAGE_AGENT_SONAR_SCANNER_ARGS = "-v ${JAVA_CACERTS}:/usr/lib/jvm/java-17-openjdk/jre/lib/security/cacerts"
  }

  stages {

    stage("Get Image and Tag") {
      when {
        anyOf {
          branch "main"
          branch "uat"
          branch "dev"
        }
      }
      steps {
        script {
          def dateFormat = new java.text.SimpleDateFormat("yyyyMMddHHmm")
          def date = new java.util.Date()

          DATE = dateFormat.format(date)

          if (BRANCH_NAME == "main") {
              DOCKER_TAG = "1.0.${DATE}"
          } else if (BRANCH_NAME == "uat") {
              DOCKER_TAG = "1.0rc${DATE}"
          } else {
              DOCKER_TAG = "1.0.dev${DATE}"
          }

          DOCKER_IMAGE = "${DOCKER_REGISTRY_PUT}/pb-agro-report-rgca-api:${DOCKER_TAG}"

          sh "cp /etc/pki/ca-trust/source/anchors/cert.pem ."
          sh "echo DOCKER_TAG=$DOCKER_TAG"
          sh "echo DOCKER_IMAGE=$DOCKER_IMAGE"
        }
      }
    }

    stage("Build Image") {
      when {
        anyOf {
          branch "main"
          branch "uat"
          branch "dev"
        }
      }
      steps {
        script {
          sh "docker build --no-cache -t ${DOCKER_IMAGE} --build-arg DOCKER_TAG=${DOCKER_TAG} ."
        }
      }
    }

    stage("Execute unit tests") {
      when {
        anyOf {
          branch "dev"
        }
      }
      steps {
        script {
          sh "docker run ${DOCKER_IMAGE} sh -c 'cd pb_agro_report_rgca_api && pytest --disable-warnings --maxfail=1 --tb=short'"
        }
      }
    }

    stage('SonarQube analysis') {
      when {
        not { tag '*' }
      }

      agent {
        docker {
          image "${DOCKER_IMAGE_AGENT_SONAR_SCANNER}"
          args "${DOCKER_IMAGE_AGENT_SONAR_SCANNER_ARGS}"
          reuseNode true
        }
      }

      steps {
        withSonarQubeEnv(installationName: 'agro-sonarqube', credentialsId: 'a6c31a75-0174-4151-9785-985b3628c7bb') {
          sh 'sonar-scanner'
        }
      }
    }

    stage('SonarQube analysis (Quality Gate)') {
      when {
        anyOf {
          branch 'dev'
        }
      }
      steps {
        // https://community.sonarsource.com/t/need-a-sleep-between-withsonarqubeenv-and-waitforqualitygate-or-it-spins-in-in-progress/2265/11
        sleep(time: 20, unit: 'SECONDS')
        waitForQualityGate abortPipeline: false
      }
    }

    stage("Push Image") {
      when {
        anyOf {
          branch "main"
          branch "uat"
          branch "dev"
        }
      }
      steps {
        script {
          withCredentials([usernamePassword(credentialsId:"DOCKER_REPOSITORY", usernameVariable: "USERNAME", passwordVariable: "PASSWORD")]) {
            sh "docker login ${DOCKER_REGISTRY_PUT} --username ${USERNAME} --password ${PASSWORD}"
            sh "docker push ${DOCKER_IMAGE}"
          }
        }
      }
    }

    stage("Deploy") {
      when {
        anyOf {
          branch "main"
          branch "uat"
          branch "dev"
        }
      }
      steps {
        script {
          def env = ""
          def profile = ""

          if (BRANCH_NAME == "main") {
              env = "prd"
						  profile = "eec-agribusiness-${env}"
          } else if (BRANCH_NAME == "uat") {
              env = "uat"
              profile = "eec-agribusiness-${env}"
          } else {
              env = "dev"
              profile = "eec-agribusiness-${env}"
          }

          sh(script: "cd kubernetes/terraform ; AWS_REGION=sa-east-1 AWS_PROFILE=${profile} terraform init -backend-config=backend-config/${env}.tfvars", returnStdout: true).trim()
          sh(script: "cd kubernetes/terraform ; AWS_REGION=sa-east-1 AWS_PROFILE=${profile} terraform apply -auto-approve -var-file=variables-${env}.tfvars -var \"app_version=\"${DOCKER_TAG}", returnStdout: true).trim()
        }
      }
    }
  }
}
