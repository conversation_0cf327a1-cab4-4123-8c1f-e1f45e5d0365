[program:uvicorn]
command=pipenv run uvicorn --host 0.0.0.0 --port 8080 --workers 1 pb_agro_report_rgca_api:api_app
process_name=%(program_name)s
numprocs=1
autostart=true
autorestart=true
startsecs=5
redirect_stderr=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
user=root
directory=/usr

[program:celery_rgca_api]
command=pipenv run celery -A pb_agro_report_rgca_api:celery_app worker --loglevel=INFO -P gevent -c 5 -E
process_name=%(program_name)s
numprocs=1
autostart=true
autorestart=true
startsecs=5
redirect_stderr=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
user=root
directory=/usr

[program:celery_beat]
command=pipenv run celery -A pb_agro_report_rgca_api:celery_app beat --loglevel=INFO
process_name=%(program_name)s
numprocs=1
autostart=true
autorestart=true
startsecs=5
redirect_stderr=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
user=root
directory=/usr
