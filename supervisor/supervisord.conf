; supervisor config file

[unix_http_server]
file=/run/supervisor.sock
chmod=0700
username=dummy
password=dummy

[supervisord]
logfile=/dev/stdout
nodaemon=true
logfile_maxbytes=0
pidfile=/run/supervisord.pid
user=root

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///run/supervisor.sock
username=dummy
password=dummy

[include]
files=/etc/supervisor.d/*.conf
