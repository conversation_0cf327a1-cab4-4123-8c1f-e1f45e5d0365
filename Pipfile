[[source]]
url = "https://nexus.agribusiness-brain.br.experian.eeca/repository/pypi-hub/simple"
verify_ssl = true
name = "nexusagribusiness-brainbrexperian"

[packages]
alembic = "==1.15.2"
amqp = "==5.3.1"
annotated-types = "==0.7.0"
anyio = "==4.9.0"
async-timeout = "==5.0.1"
agri-data-crypto = "==0.1.12"
billiard = "==4.2.1"
boto3 = "==1.37.28"
botocore = "==1.37.28"
celery = "==5.4.0"
certifi = "==2025.1.31"
cffi = "==1.17.1"
chardet = "==5.2.0"
charset-normalizer = "==3.4.1"
click-didyoumean = "==0.3.1"
click-plugins = "==1.1.1"
click-repl = "==0.3.0"
click = "==8.1.3"
colorama = "==0.4.6"
dnspython = "==2.7.0"
et-xmlfile = "==2.0.0"
exceptiongroup = "==1.2.2"
fastapi-sqlalchemy = "==0.2.1"
fastapi = "==0.115.12"
gevent = "==24.11.1"
geoalchemy2 = "*"
greenlet = "==3.1.1"
h11 = "==0.16.0"
httptools = "==0.6.4"
iam-token-validation = "==0.1.3"
idna = "==3.10"
jmespath = "==1.0.1"
kombu = "==5.5.2"
numpy = "==2.2.4"
openpyxl = "==3.1.5"
orjson = "==3.10.18"
pandas = "==2.2.3"
geopandas = "==1.0.1"
pb-secret-manager-to-env-python = "==0.1.0"
pillow = "==11.1.0"
prompt-toolkit = "==3.0.50"
psycopg2 = "==2.9.10"
pycparser = "==2.22"
pycurl = "==7.45.6"
pydantic-core = "==2.33.0"
pydantic = "==2.11.1"
pymongo = "==4.12.0"
pymsteams = "==0.2.5"
python-dateutil = "==2.9.0.post0"
python-dotenv = "==1.1.0"
python-multipart = "==0.0.20"
pytz = "==2025.2"
pyyaml = "==6.0.2"
redis = "==5.0.5"
requests = "==2.32.3"
s3transfer = "==0.11.4"
six = "==1.17.0"
sniffio = "==1.3.1"
sqlalchemy = "==2.0.39"
starlette = "==0.46.1"
typing-extensions = "==4.13.1"
typing-inspection = "==0.4.0"
tzdata = "==2025.2"
uvicorn = "==0.27.1"
uvloop = "==0.21.0"
vine = "==5.1.0"
watchfiles = "==1.0.4"
wcwidth = "==0.2.13"
websockets = "==15.0.1"
zope-event = "==5.0"
zope-interface = "==7.2"
aiohttp = "3.11.18"
nest-asyncio = "1.6.0"

[dev-packages]
faker = "*"
pre-commit = "*"
isort = "*"
flake8 = "*"
bandit = "*"
gitlint = "*"
black = "*"
pytest = "*"
coverage = "*"
coverage-badge = "*"
jupyter = "*"

[requires]
python_version = "3.12"
