# Sonar Host
sonar.host.url=https://sonarqube.agribusiness-brain.br.experian.eeca

# Unique key to identify the project
sonar.projectKey=pb_agro_report_rgca

# Python version
sonar.python.version=3.10

# For source control management
sonar.scm.disabled=true

# Encoding of the source files
sonar.sourceEncoding=UTF-8

# Within the directories defined by sonar.tests, subset of files that will be considered as tests
sonar.test.inclusions=\
    **/test*.py

# Exclude following files from Sonarqube code analysis to reduce noise
sonar.exclusions=\
    alembic/**, \
    **/alembic/**, \
    **.conf.py, \
    .env, \
    **/.env/**, \
    **/env/**, \
    **/env2/**, \
    **/venv/**, \
    **/__init__.py, \
    **/.devcontainer/**, \
    **/.mypy_cache/**, \
    **/.pytest_cache/**, \
    **/.vscode/**, \
    **/.coverage/**, \
    **/log/**, \
    **/media/**, \
    **/migrations/**, \
    **/dist/**, \
    **/coverage/**, \
    **/*.pyc, \
    **/coverage.xml \
    source/coverage/**, \
    source/lambda/coverage/**, \
    source/ui/coverage/**, \
    source/cognito-trigger/jest.config.js, \
    source/ui/src/jest.config.js, \
    source/cognito-trigger/setJestEnvironmentVariables.ts, \
    **.mo, \
    **.po, \
    **.js, \
    htmlcov/**, \
    source/ui/src/setupTests.ts


# Exclude following files from Sonarqube coverage reporting
sonar.coverage.exclusions=\
    **/tests/**

#   Comma-separated list of ant pattern describing paths to coverage reports, relative to projects
#   root. Leave unset to use the default ("coverage-reports/*coverage-*.xml").
sonar.python.coverage.reportPaths=coverage.xml
