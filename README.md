# Agro Report - Worker

Description: Repository for Agro Report Worker.

Test coverage: ![Coverage](coverage.svg)

## Images and Volumes (Required to run celery)

docker pull rabbitmq:3.10.6-management-alpine

docker volume create rq
docker volume create rd

docker run --name rq -d -p 5672:5672 -v agrosatrq:/var/lib/rabbitmq -e RABBITMQ_DEFAULT_USER=admin -e RABBITMQ_DEFAULT_PASS=@dmin123 rabbitmq:3.10.6-management-alpine

docker run --name rd -d -p 6379:6379 -v agrosatrd:/data redis:7.0.5-alpine redis-server --appendonly yes

`After create the volumes, if stop, run:`

docker start <nome>

## Requirements

- Python 3.10
- pipenv

## Install Pipenv

After installing python, you need to install the poetry package to run `make` command.

```shell
$ pip install pipenv
```

## Install Agro Report Worker

With all the requirements installed, inside the project directory, run the command below:

```shell
$ make install
```

## Activate and deactivate environment

To activate a virtual environment run the commands below and to deactivate use shortcut `CTRL+D`:

```shell
$ python3.10 -m poetry shell  # activate
```

## Make commands

- `make install`: Installs the project dependencies;
- `make uninstall`: Remove virtual environments associated with the project.

## Start project

Comando ./run_server.sh pra iniciar a api do repositorio

Comando ./worker_lite.sh pra iniciar o worker (celery)
