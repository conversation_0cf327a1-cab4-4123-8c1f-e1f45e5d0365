.ONESHELL:

SHELL			:=$(shell which bash)
POETRY_CMD		:=pipenv
ENV_PATH		?=$(shell $(POETRY_CMD) env info -p)

export POETRY_VIRTUALENVS_IN_PROJECT=false

ifeq ($(GDAL_VERSION), 3.7.1)
	GDAL_VERSION:="$(GDAL_VERSION).1"
endif

.PHONY: install
install:
	@$(POETRY_CMD) install --no-interaction --no-ansi
	@$(POETRY_CMD) run pre-commit install -f -t pre-commit --hook-type commit-msg

.PHONY: uninstall
uninstall:
ifneq ($(wildcard $(ENV_PATH)),)
	@$(POETRY_CMD) run pre-commit uninstall -t pre-commit -t commit-msg
	@$(POETRY_CMD) env remove $(shell basename $(ENV_PATH))
endif

.PHONY: lint
lint:
ifneq ($(wildcard $(ENV_PATH)),)
	@$(POETRY_CMD) run pre-commit run
endif

.PHONY: reinstall
reinstall:
	@$(MAKE) uninstall
	@$(MAKE) install

.PHONY: test
test:
	coverage run -m pytest
	coverage report -m --omit="*test*"
