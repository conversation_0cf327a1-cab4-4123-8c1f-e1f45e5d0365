"""Initial migration

Revision ID: 556461e94352
Revises:
Create Date: 2024-08-15 11:40:18.119061

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "556461e94352"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "report_rgca",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("requester", sa.String(), nullable=False),
        sa.<PERSON>umn("webhook", sa.String(), nullable=True),
        sa.Column("status", sa.String(), server_default="PENDING", nullable=True),
        sa.Column("filename", sa.String(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_report_rgca_id"), "report_rgca", ["id"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_report_rgca_id"), table_name="report_rgca")
    op.drop_table("report_rgca")
    # ### end Alembic commands ###
