"""added new column succeeded_scores to rgca model

Revision ID: 73f557335979
Revises: f4b18de368f0
Create Date: 2024-10-22 17:34:43.487304

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "73f557335979"
down_revision: Union[str, None] = "f4b18de368f0"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "report_rgca", sa.Column("succeeded_scores", sa.Integer(), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("report_rgca", "succeeded_scores")
    # ### end Alembic commands ###
