"""

Revision ID: 187e359cfe6d
Revises: 556461e94352
Create Date: 2024-08-21 12:52:32.503674

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "187e359cfe6d"
down_revision: Union[str, None] = "556461e94352"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "report_rgca", sa.Column("client_document", sa.String(), nullable=False)
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("report_rgca", "client_document")
    # ### end Alembic commands ###
