"""empty message

Revision ID: f4b18de368f0
Revises: 187e359cfe6d
Create Date: 2024-09-05 15:44:26.633391

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "f4b18de368f0"
down_revision: Union[str, None] = "187e359cfe6d"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("report_rgca", sa.Column("external_id", sa.Integer(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("report_rgca", "external_id")
    # ### end Alembic commands ###
