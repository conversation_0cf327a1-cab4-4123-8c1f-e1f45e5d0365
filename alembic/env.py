import os
from logging.config import fileConfig

from pb_sm_helper.sm_helper import Sm<PERSON>el<PERSON>
from sqlalchemy import engine_from_config, pool

from alembic import context
from pb_agro_report_rgca_api import models

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

SM_AGRO_REPORT = os.getenv("SM_AGRO_REPORT")

if SM_AGRO_REPORT:
    SmHelper.load_as_env([SM_AGRO_REPORT])

section = config.config_ini_section

DB_USER_APP = os.getenv("DB_USER_APP")
DB_PASSWORD_APP = os.getenv("DB_PASSWORD_APP")
DB_HOST_APP = os.getenv("DB_HOST_APP")
DB_PORT_APP = os.getenv("DB_PORT_APP")
DB_DATABASE_APP = os.getenv("DB_DATABASE_APP")

DB_URL = f"postgresql://{DB_USER_APP}:{DB_PASSWORD_APP}@{DB_HOST_APP}:{DB_PORT_APP}/{DB_DATABASE_APP}"  # noqa
config.set_section_option(section, "DB_URL", DB_URL)

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata
target_metadata = models.Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """

    context.configure(
        url=DB_URL,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    config = {"sqlalchemy.url": DB_URL}
    connectable = engine_from_config(
        config,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(connection=connection, target_metadata=target_metadata)

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
