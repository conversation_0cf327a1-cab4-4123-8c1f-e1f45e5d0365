deployment:
  host: pb-agro-report-rgca-api.agribusiness-brain-uat.br.experian.eeca

envVars:
  SM_AGRO_REPORT: "SM_RGCA_UAT"
  ENV: "uat"
  DEBUG: "false"
  CELERY_BROKER_URL: "sqs://"
  AGRO_REPORT_RGCA_QUEUE_URL: "https://sqs.sa-east-1.amazonaws.com/635729029167/pb-agro-report-rgca-uat"
  AGRO_REPORT_API_BASE_URL: "http://pb-agro-report-api-service.platform-md-agro-report-uat.svc.cluster.local/agroreport"
  REQUESTS_HTTPS_VERIFY: "true"
  API_USERS_BASE_URL: "http://pb-api-users-service.platform-core-uat/users/v1"
  AGRO_SCORE_LAMBDA: "brainag-pb-bag-agro-score-uat-lambda-feQXcYeGGHg7"
  AGRO_SCORE_LAMBDA_REGION: "sa-east-1"
  RGCA_BUCKET: "pb-agro-report-rgca-api-uat"
  URL_DIGITAL_PAAS: "https://uat-api.serasaexperian.com.br"
  AGRO_RELATED_API_BASE_URL: "http://pb-api-agri-related-check-service.platform-core-uat.svc.cluster.local"
  CLOUD_RANGER_BASE_URL: "http://agri-cloudranger-api-service.agrowatch-core-uat.svc.cluster.local"
  DELIVERY_API_URL: "https://uat-plataforma-backend.brainag.app/delivery-api"
  CLOUD_RANGER_LOGIN_URL: "https://uat-api.serasaexperian.com.br"
  MONGO_DB_DATABASE: "agro_data_service_mod_dev"
  AGROWATCH_BASE_URL: "http://agro-watch-api-service.agrowatch-core-uat.svc.cluster.local"
  AGROWATCH_IAM_URL: "https://uat-api.serasaexperian.com.br"
  AWS_LAMBDA_RECTIFIED_CAR: "lambda-pb-lambda-property-history-uat"
  DB_NAME_EXPERIAN_AGRI_PRIVATE: "experian_agri_private_uat"
  DB_NAME_EXPERIAN_AGRI_PUBLIC: "experian_agri_public_uat"
  DB_HOST_EXPERIAN_AGRI_PUBLIC: "agro-uat.cluster-crcepn4ytf4h.sa-east-1.rds.amazonaws.com"
  DB_PORT_EXPERIAN_AGRI_PUBLIC: "5432"
  DB_USER_EXPERIAN_AGRI_PUBLIC: "svc_agro_report_rgca_uat"
