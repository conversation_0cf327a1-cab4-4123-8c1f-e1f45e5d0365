deployment:
  repository: dockerhub.agribusiness-brain.br.experian.eeca/pb-agro-report-rgca-api
  pullPolicy: IfNotPresent
  strategy:
    type: RollingUpdate
  production:
    replicaCount: 1

app:
  labels:
    gearr: "0"

imagePullSecrets:
  - name: regcred-dockerhub

nameOverride: "pb-agro-report-rgca-api"

fullnameOverride: "pb-agro-report-rgca-api"

serviceAccount:
  create: true
  annotations: { }
  name: "pb-agro-report-rgca-api"

podAnnotations: { }

podSecurityContext: { }

securityContext: { }

containerPort: 8080

service:
  name: api
  type: ClusterIP
  port: 80
  containerPort: 8080


livenessProbe:
  httpGet:
    path: /api/health-check
    port: 8080
  initialDelaySeconds: 30
  timeoutSeconds: 30
  periodSeconds: 10

readinessProbe:
  httpGet:
    path: /api/health-check
    port: 8080
  initialDelaySeconds: 30
  timeoutSeconds: 30
  periodSeconds: 10
  tcpSocket:

resources:
  limits:
    cpu: 1000m
    memory: 512Mi
  requests:
    cpu: 500m
    memory: 512Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 60

nodeSelector: { }

tolerations: [ ]

affinity: { }

virtualService:
  enabled: true
  match:
    - uri:
        prefix: /
    - uri:
        exact: /
