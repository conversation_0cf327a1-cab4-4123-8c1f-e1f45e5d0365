deployment:
  host: pb-agro-report-rgca-api.agribusiness-brain.br.experian.eeca

envVars:
  SM_AGRO_REPORT: "SM_RGCA_PROD"
  ENV: "prd"
  DEBUG: "false"
  CELERY_BROKER_URL: "sqs://"
  AGRO_REPORT_RGCA_QUEUE_URL: "https://sqs.sa-east-1.amazonaws.com/488558083821/pb-agro-report-rgca-prod"
  AGRO_REPORT_API_BASE_URL: "http://pb-agro-report-api-service.platform-md-agro-report-prd.svc.cluster.local/agroreport"
  REQUESTS_HTTPS_VERIFY: "true"
  API_USERS_BASE_URL: "http://pb-api-users-service.platform-core-prd/users/v1"
  AGRO_SCORE_LAMBDA: "brainag-pb-bag-agro-score-prod-lambda-UfVdQqZWcWfE"
  AGRO_SCORE_LAMBDA_REGION: "sa-east-1"
  RGCA_BUCKET: "pb-agro-report-rgca-api-prod"
  URL_DIGITAL_PAAS: "https://api.serasaexperian.com.br"
  AGRO_RELATED_API_BASE_URL: "http://pb-api-agri-related-check-service.platform-core-prd.svc.cluster.local"
  CLOUD_RANGER_BASE_URL: "http://agri-cloudranger-api-service.agrowatch-core-prd.svc.cluster.local"
  DELIVERY_API_URL: "https://plataforma-backend.brainag.app/delivery-api"
  CLOUD_RANGER_LOGIN_URL: "https://api.serasaexperian.com.br"
  MONGO_DB_DATABASE: "agro_data_service"
  AGROWATCH_BASE_URL: "http://agro-watch-api-service.agrowatch-core-prod.svc.cluster.local"
  AGROWATCH_IAM_URL: "https://api.serasaexperian.com.br"
  AWS_LAMBDA_RECTIFIED_CAR: "lambda-pb-lambda-property-history-prod"
  DB_NAME_EXPERIAN_AGRI_PRIVATE: "experian_agri_private"
  DB_NAME_EXPERIAN_AGRI_PUBLIC: "experian_agri_public"
  DB_HOST_EXPERIAN_AGRI_PUBLIC: "agro.cluster-crcepn4ytf4h.sa-east-1.rds.amazonaws.com"
  DB_PORT_EXPERIAN_AGRI_PUBLIC: "5432"
