env              = "dev"
k8s_cluster      = "agribusiness-eks-dev"
helm_values_file = "../helm/develop/values.yaml"

s3_bucket_names = [
    "pb-agro-report-rgca-api-dev"
]

secrets_names = [
    "SM_RGCA_DEV", "SM_BRAIN_AES_ENCRYPTION_KEYS_V1"
]

sqs_queue_names = [
    "pb-agro-report-rgca-dev",
]

kms_aliases = [
    "s3-dev", "eks_secret_agribusiness-eks-dev", "sqs-dev"
]

lambda_functions = [
    {
        name       = "brainag-pb-bag-agro-score-dev-lambda-oEw4YZ1niVov"
        region     = "sa-east-1"
        account_id = "************"
    },
    {
        name       = "lambda-pb-lambda-property-history-dev"
        region     = "sa-east-1"
        account_id = "************"
    }
]
