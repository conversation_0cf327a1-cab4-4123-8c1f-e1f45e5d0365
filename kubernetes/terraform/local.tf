locals {
  account_id               = data.aws_caller_identity.current.account_id
  region                   = data.aws_region.current.name
  namespace                = "platform-md-agro-report-${var.env}"
  app_name                 = "pb-agro-report-rgca-api"
  helm_default_values_file = "../helm/default_values.yaml"
  chart                    = {
    repository = "oci://dockerhub.agribusiness-brain.br.experian.eeca"
    name       = "agro-sre/hchart-stateless"
    version    = "0.3.9"
  }

  oidc_provider = substr(data.aws_eks_cluster.cluster.identity[0].oidc[0].issuer, 8, -1)
}
