resource "helm_release" "app_release" {
  name             = local.app_name
  namespace        = local.namespace
  create_namespace = true
  timeout          = 900

  repository = local.chart.repository
  chart      = local.chart.name
  version    = local.chart.version

  values = [
    file(local.helm_default_values_file),
    file(var.helm_values_file)
  ]

  set {
    name  = "serviceAccount.annotations.eks\\.amazonaws\\.com/role-arn"
    value = module.application_permissions.application_role.arn
  }

  set {
    name  = "serviceAccount.name"
    value = local.app_name
  }

  set {
    name  = "deployment.production.image.tag"
    value = var.app_version
  }
}
