env              = "uat"
k8s_cluster      = "agribusiness-eks-uat"
helm_values_file = "../helm/uat/values.yaml"


s3_bucket_names = [
    "pb-agro-report-rgca-api-uat"
]

secrets_names = [
    "SM_RGCA_UAT", "SM_BRAIN_AES_ENCRYPTION_KEYS_V1"
]

sqs_queue_names = [
    "pb-agro-report-rgca-uat",
]

kms_aliases = [
    "s3-uat",  "sqs-uat"
]

lambda_functions = [
    {
        name       = "brainag-pb-bag-agro-score-uat-lambda-feQXcYeGGHg7"
        region     = "sa-east-1"
        account_id = "************"
    },
    {
        name       = "lambda-pb-lambda-property-history-uat"
        region     = "sa-east-1"
        account_id = "************"
    }
]
