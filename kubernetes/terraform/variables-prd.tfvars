env              = "prd"
k8s_cluster      = "agribusiness-eks-prod"
helm_values_file = "../helm/prod/values.yaml"

s3_bucket_names = [
    "pb-agro-report-rgca-api-prod"
]

secrets_names = [
    "SM_RGCA_PROD", "SM_BRAIN_AES_ENCRYPTION_KEYS_V1"
]

sqs_queue_names = [
    "pb-agro-report-rgca-prod",
]

kms_aliases = [
    "s3-prod", "sqs-prod"
]

lambda_functions = [
    {
        name       = "brainag-pb-bag-agro-score-prod-lambda-UfVdQqZWcWfE"
        region     = "sa-east-1"
        account_id = "************"
    },
    {
        name       = "lambda-pb-lambda-property-history-prod"
        region     = "sa-east-1"
        account_id = "************"
    }
]
