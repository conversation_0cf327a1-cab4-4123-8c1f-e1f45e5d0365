variable "env" {
  type = string
}
variable "k8s_cluster" {
  type = string
}

variable "helm_values_file" {
  type = string
}

variable "sqs_queue_names" {
  type    = list(string)
  default = []
}

variable "s3_bucket_names" {
  type    = list(string)
  default = []
}

variable "app_version" {
  type = string
}

variable "secrets_names" {
  type  = list(string)
  default = []
}

variable "kms_aliases" {
  type    = list(string)
  default = []
}


variable "lambda_functions" {
  type = list(object({
    name       = string
    account_id = string
    region     = string
  }))
}
