data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

module "application_permissions" {
  source = "git::ssh://***********************/datalabter/tf-k8s-application-permissions.git"

  account_id    = data.aws_caller_identity.current.account_id
  oidc_provider = local.oidc_provider
  env           = var.env
  namespace     = local.namespace
  app_name      = local.app_name


  sqs_queues = [
    for queue_name in var.sqs_queue_names : {
      name       = queue_name
      region     = data.aws_region.current.name
      permission = {
        send    = true
        receive = true
      }
    }
  ]

  s3_buckets = [
    for bucket_name in var.s3_bucket_names : {
      name       = bucket_name
      permission = {
        read   = true
        write  = true
        delete = false
      }
    }
  ]

  extra_tags = {
    Application = local.app_name
    Env         = var.env
  }

  secrets = [
    for secret_name in var.secrets_names : {
      id       = {
        name = secret_name
        region = local.region
      }
      permission = {
        read   = true
      }
    }
  ]

  kms_keys = [
    for kms_alias in var.kms_aliases : {
      id = {
        alias = kms_alias
      }
      permission = {
        encrypt = true
        decrypt = true
      }
    }
  ]

  lambda_functions = [
    for lambda_function in var.lambda_functions : {
      id = {
        region     = lambda_function.region
        account_id = lambda_function.account_id
        name       = lambda_function.name
      }
      permission = {
        invoke = true
      }
    }
  ]
}
